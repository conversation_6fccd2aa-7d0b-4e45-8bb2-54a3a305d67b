# Lega Pauper Adriatica — App

Next.js (App Router) application with Supabase authentication via magic links, calendar management, and admin features.

This README summarizes the recent authentication fixes, build improvements, and how to configure and run the project.

---

## Quick Start

1) Install dependencies

```bash
npm install
```

2) Configure environment

Create a .env.local file with at least:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
# Used to build absolute emailRedirectTo and to match Supabase Auth Site URL
NEXT_PUBLIC_SITE_URL=http://localhost:3000
# Optional: Comma-separated list of allowed development origins
ALLOWED_DEV_ORIGINS=localhost:3000,127.0.0.1:3000
```

See `.env.example` for a complete configuration template.

3) Run dev server

```bash
npm run dev
```

Open http://localhost:3000.

4) Production build

```bash
npm run build
npm run start
```

---

## Authentication Overview (Supabase Magic Links)

- Login page: /login
  - Two actions:
    - Accedi → signInWithOtp(..., { shouldCreateUser: false })
    - Registrati → signInWithOtp(..., { shouldCreateUser: true })
  - UI cooldown: 60s between link requests per email (client-only, via useCooldown)
  - Next.js 15 requirement: the page uses useSearchParams and is wrapped in Suspense to avoid CSR bailout errors.

- Confirmation endpoint: /auth/confirm
  - Verifies the magic link token_hash securely on the server.
  - Uses Supabase createServerClient with a cookie adapter bound to the NextResponse.
  - On success, attaches auth cookies to the response before redirect, ensuring the client is logged in upon arrival.
  - Redirect target derived from next or defaults to /profile.

- Auth error page: /auth/error

Key files:
- src/app/login/page.tsx — Magic link UI, Suspense wrapper, redirects if already logged in
- src/app/auth/confirm/route.ts — Server route that sets Supabase session cookies on redirect
- src/lib/utils/site.ts — getSiteUrl utility (uses NEXT_PUBLIC_SITE_URL or window.origin)
- src/lib/providers/AuthProvider — User/session context on the client

---

## Configuration Notes

- Supabase Dashboard → Authentication → URL Configuration
  - Site URL should match NEXT_PUBLIC_SITE_URL (prod: your domain; dev: http://localhost:3000)
  - Redirect URLs: add your trusted origins/pages used by auth flows

- Email Templates
  - Use token_hash in the magic link and point it to /auth/confirm.
  - Example target: {{ .SiteURL }}/auth/confirm?token_hash={{ .TokenHash }}&type=email

- Environment Variables
  - Do not expose service role keys in the client.
  - Only NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are required for the browser client.
  - NEXT_PUBLIC_SITE_URL is recommended to avoid host mismatches.

---

## Admin Role Setup

See docs/admin-setup.md for:
- SQL migrations and policies
- Assigning the admin role to a user
- Troubleshooting common RLS/policy issues

---

## Calendar & Filters (Developer Notes)

- Hooks involved:
  - src/hooks/useCalendarLogic.ts
  - src/hooks/useCardViewTournaments.ts
  - src/hooks/useFilteredTournaments.ts
- We removed unnecessary filtersString from useMemo dependency arrays (to satisfy react-hooks/exhaustive-deps) while keeping deep-compare where appropriate in effects. No functional changes to filtering logic.

---

## Recent Fixes and Improvements

1) Reliable session cookies after magic link
   - /auth/confirm now uses createServerClient with a response-bound cookie store and attaches cookies before redirect.
   - Fixes the issue where users landed back on /login not authenticated.

2) Login page stability on Next 15
   - Wrapped /login in Suspense to handle useSearchParams properly and avoid CSR bailout errors.

3) Safer null handling
   - Profile page updated to handle potentially null user and avoid TypeScript errors.

4) Lint cleanup
   - Removed unused imports/variables and unnecessary dependencies in hooks to eliminate warnings.

---

## Troubleshooting

- After clicking the magic link, I’m redirected but not logged in
  - Ensure NEXT_PUBLIC_SITE_URL matches the current domain.
  - In the browser Network tab, check /auth/confirm response has Set-Cookie for the Supabase session.
  - Verify Supabase Auth → Site URL aligns with the environment.

- CSR bailout / useSearchParams warning
  - The /login page is wrapped in Suspense; if you create other pages using useSearchParams, wrap them similarly.

- Email security / scanners
  - Using token_hash and server-side verification at /auth/confirm mitigates email prefetch issues.

---

## Testing the Magic Link Flow (Manual)

1) From /login, request a magic link with a valid email.
2) Open the email and click the link; it should hit /auth/confirm.
3) You should be redirected to /profile (or next) already authenticated.
4) Refresh to confirm the session persists.
5) Repeat with an admin email to verify admin routes.

---

## Additional Docs

- docs/admin-setup.md — Configurazione Ruolo Amministratore
- docs/future-player-authentication.md — Strategy for future player auth rollout

---

## Scripts

- npm run dev — Start development server
- npm run build — Create production build
- npm run start — Start production server
- npm run create-admin — Create admin user (interactive script)

### Creating Admin Users

To create an admin user, run:

```bash
npm run create-admin
```

This interactive script will:
1. Validate your environment configuration
2. Prompt for an admin email address
3. Create the user in Supabase Auth
4. Assign admin role in the database
5. Send a magic link email for account setup

---

## License

Proprietary. All rights reserved.
