# Tournament Organizer Feature - Planning Document

## Overview
This document outlines the implementation plan for a Tournament Organizer feature that will be integrated into the existing `TournamentDetails.tsx` component. The feature will be **ADMIN-only** and will provide comprehensive tournament management capabilities including Swiss pairing system, round management, and result tracking.

## 1. UI Components and Changes

### 1.1 Tournament Creation Button
**Location**: Top-right corner of `TournamentDetails.tsx`
**Design**:
- **Desktop**: Square button with rounded corners (`rounded-lg`)
- **Mobile**: Same design, responsive sizing
- **Colors**: Green background (`bg-green-600 hover:bg-green-700`)
- **Icon**: White "+" icon (using Lucide React `Plus` component)
- **Tooltip**: "create tournament" (using existing tooltip pattern)
- **States**:
  - Initial: Green button with "+" icon
  - After creation: Changes to "eye" icon (`Eye` from Lucide React)
  - Tooltip changes to "view tournament"

### 1.2 Confirmation Modal
**Design**: Consistent with existing modal pattern (`Modal` component from `@/components/ui/Modal`)
**Content**:
- **Text**: "Do you want to create a tournament for this event?"
- **Buttons**:
  - Cancel: Blue background (`bg-blue-600 hover:bg-blue-700`), white text
  - Proceed: Green background (`bg-green-600 hover:bg-green-700`), white text
- **Styling**: Uses existing modal styling with blue gradient background

### 1.3 Tournament Panel
**Design**: Fullscreen panel (similar to existing modals but `size="full"`)
**Header**:
- Close "X" icon top-right (using existing `X` from Lucide React)
- Tournament title
- Settings button (gear icon) for round configuration

**Layout**:
- **Left Side**: Player list (alphabetical order)
- **Center**: Pairings/matches display
- **Top Center**: Timer (when round is active)
- **Right Side**: Controls and actions

### 1.4 Tournament Setup Modal
**Triggers**: First access or settings button click
**Fields**:
- Number of rounds (integer input, 1-10 range)
- Round duration in minutes (integer input, 30-120 range)
- Form validation using existing `FormField` and `Input` components

### 1.5 Player Management
**Components**:
- Player list with refresh button (`RefreshCw` icon)
- Alphabetical sorting
- Visual indicators for bye assignments
- Player status indicators (active, dropped, bye)

### 1.6 Pairing Interface
**Components**:
- Match cards showing player pairings
- Result dropdowns for each match
- "Generate Pairings" button
- "Confirm Round" button
- "Cancel Pairings" button (before confirmation)

### 1.7 Timer Component
**Design**:
- Large, prominent display at top center
- Format: MM:SS
- Color changes based on time remaining (green → yellow → red)
- Start/pause/reset controls

### 1.8 Results Interface
**Components**:
- Match result dropdowns: `2-0`, `2-1`, `1-2`, `0-2`, `1-0`, `0-1`, `0-0`
- Bulk result entry
- Result correction capabilities
- Final standings table

## 2. Database Schema Updates

### 2.1 Current Database Structure Analysis
Based on Supabase MCP analysis, the current relevant tables are:

#### Existing `tournaments` table:
- `id` (uuid, PK)
- `title` (text, NOT NULL)
- `date` (timestamptz, NOT NULL)
- `store_id` (uuid, FK to stores.id, nullable)
- `time_start` (time, NOT NULL)
- `time_end` (time, NOT NULL)
- `format` (text, NOT NULL)
- `max_players` (integer, NOT NULL)
- `description` (text, nullable)
- `prize_pool` (text, nullable)
- `price` (numeric, NOT NULL)
- `season_id` (uuid, FK to seasons.id, nullable)
- `created_at`, `updated_at` (timestamptz)

#### Existing `tournament_registrations` table:
- `id` (uuid, PK)
- `tournament_id` (uuid, FK, NOT NULL)
- `player_id` (uuid, FK, NOT NULL)
- `registration_date` (timestamptz, NOT NULL)
- `deck_name` (text, nullable)
- `archetype_id` (uuid, FK to archetypes.id, nullable)
- `deck_list_url` (text, nullable)
- `deck_list` (text, nullable)

#### Existing `tournament_results` table:
- `id` (uuid, PK)
- `tournament_id` (uuid, FK, NOT NULL)
- `player_id` (uuid, FK, NOT NULL)
- `position` (integer, NOT NULL)
- `points` (integer, NOT NULL)
- `wins` (integer, default 0)
- `draws` (integer, default 0)
- `losses` (integer, default 0)
- `deck_list_url` (text, nullable)
- `created_at` (timestamptz)

### 2.2 Required New Tables

#### `tournament_organizer_settings`
```sql
CREATE TABLE tournament_organizer_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tournament_id UUID NOT NULL REFERENCES tournaments(id) ON DELETE CASCADE,
  rounds_total INTEGER NOT NULL CHECK (rounds_total > 0 AND rounds_total <= 15),
  round_duration_minutes INTEGER NOT NULL CHECK (round_duration_minutes >= 30 AND round_duration_minutes <= 180),
  current_round INTEGER DEFAULT 0 CHECK (current_round >= 0),
  status TEXT NOT NULL DEFAULT 'setup' CHECK (status IN ('setup', 'active', 'paused', 'completed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tournament_id)
);
```

#### `tournament_rounds`
```sql
CREATE TABLE tournament_rounds (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tournament_id UUID NOT NULL REFERENCES tournaments(id) ON DELETE CASCADE,
  round_number INTEGER NOT NULL CHECK (round_number > 0),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'completed')),
  started_at TIMESTAMP WITH TIME ZONE,
  ended_at TIMESTAMP WITH TIME ZONE,
  duration_minutes INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tournament_id, round_number)
);
```

#### `tournament_pairings`
```sql
CREATE TABLE tournament_pairings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  round_id UUID NOT NULL REFERENCES tournament_rounds(id) ON DELETE CASCADE,
  player1_id UUID REFERENCES players(id) ON DELETE CASCADE,
  player2_id UUID REFERENCES players(id) ON DELETE CASCADE,
  table_number INTEGER,
  is_bye BOOLEAN DEFAULT FALSE,
  result TEXT CHECK (result IN ('2-0', '2-1', '1-2', '0-2', '1-0', '0-1', '0-0')),
  winner_id UUID REFERENCES players(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CHECK (
    (is_bye = TRUE AND player2_id IS NULL AND player1_id IS NOT NULL) OR 
    (is_bye = FALSE AND player1_id IS NOT NULL AND player2_id IS NOT NULL)
  )
);
```

#### `tournament_player_stats`
```sql
CREATE TABLE tournament_player_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tournament_id UUID NOT NULL REFERENCES tournaments(id) ON DELETE CASCADE,
  player_id UUID NOT NULL REFERENCES players(id) ON DELETE CASCADE,
  match_points INTEGER DEFAULT 0,
  game_wins INTEGER DEFAULT 0,
  game_losses INTEGER DEFAULT 0,
  matches_played INTEGER DEFAULT 0,
  byes_received INTEGER DEFAULT 0,
  is_dropped BOOLEAN DEFAULT FALSE,
  omw_percentage DECIMAL(5,2) DEFAULT 0.00,
  gwp_percentage DECIMAL(5,2) DEFAULT 0.00,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tournament_id, player_id)
);
```

### 2.3 Table Modifications

#### Add tournament organizer flag to tournaments table
```sql
ALTER TABLE tournaments ADD COLUMN has_organizer BOOLEAN DEFAULT FALSE;
ALTER TABLE tournaments ADD COLUMN organizer_created_at TIMESTAMP WITH TIME ZONE;
```

### 2.4 RLS Policies (leveraging existing `is_admin()` function)

#### Tournament organizer settings - Admin only
```sql
CREATE POLICY "Admin can manage tournament organizer settings" 
ON tournament_organizer_settings FOR ALL 
USING (is_admin());
```

#### Tournament rounds - Admin during setup/active, public read when completed
```sql
CREATE POLICY "Admin can manage tournament rounds" 
ON tournament_rounds FOR ALL 
USING (is_admin());

CREATE POLICY "Public can view completed tournament rounds" 
ON tournament_rounds FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM tournament_organizer_settings tos 
    WHERE tos.tournament_id = tournament_rounds.tournament_id 
    AND tos.status = 'completed'
  )
);
```

#### Tournament pairings - Similar pattern
```sql
CREATE POLICY "Admin can manage tournament pairings" 
ON tournament_pairings FOR ALL 
USING (is_admin());

CREATE POLICY "Public can view completed tournament pairings" 
ON tournament_pairings FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM tournament_organizer_settings tos 
    JOIN tournament_rounds tr ON tr.tournament_id = tos.tournament_id
    WHERE tr.id = tournament_pairings.round_id 
    AND tos.status = 'completed'
  )
);
```

#### Tournament player stats - Similar pattern
```sql
CREATE POLICY "Admin can manage tournament player stats" 
ON tournament_player_stats FOR ALL 
USING (is_admin());

CREATE POLICY "Public can view completed tournament player stats" 
ON tournament_player_stats FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM tournament_organizer_settings tos 
    WHERE tos.tournament_id = tournament_player_stats.tournament_id 
    AND tos.status = 'completed'
  )
);
```

## 3. Tournament Logic Flow

### 3.1 Tournament Creation Flow
1. **Admin clicks create button** → Confirmation modal appears
2. **Admin confirms** → Tournament organizer record created in `tournament_organizer_settings`
3. **Button state changes** → "+" becomes "eye" icon, `tournaments.has_organizer = true`
4. **First access** → Setup modal for rounds/duration configuration

### 3.2 Swiss Pairing Algorithm
**Round 1**: Random pairing
- Fetch registered players from `tournament_registrations`
- Shuffle players randomly
- Pair sequentially (1v2, 3v4, etc.)
- If odd number → lowest player gets bye (stored as `is_bye = true`)

**Subsequent Rounds**:
1. **Group by match points** from `tournament_player_stats` (3 = win, 1 = draw, 0 = loss)
2. **Within each group**: Pair players who haven't played each other (check `tournament_pairings` history)
3. **If odd group size**: Drop lowest player to next group
4. **Bye assignment**: Only to players who haven't received one (`byes_received = 0`)
5. **Conflict resolution**: Avoid repeat pairings when possible

### 3.3 Round Management Flow
1. **Generate Pairings** → Create records in `tournament_pairings` with `result = NULL`
2. **Admin Review** → Can delete and regenerate if needed (late arrivals, etc.)
3. **Confirm Round** → Update `tournament_rounds.status = 'active'`, start timer
4. **Result Entry** → Admin updates `tournament_pairings.result` and `winner_id`
5. **Round Completion** → Update `tournament_player_stats`, create next round

### 3.4 Scoring System
**Match Points** (stored in `tournament_player_stats.match_points`):
- Win: 3 points
- Draw: 1 point
- Loss: 0 points
- Bye: 3 points

**Game Statistics** (stored in `tournament_player_stats`):
- `game_wins`, `game_losses` calculated from match results
- `matches_played` incremented each round (excluding byes)
- `byes_received` incremented when player gets bye

**Tiebreakers**:
1. **Match Points** (primary)
2. **Opponent Match Win %** (OMW%) - calculated from opponents' match records
3. **Game Win %** (GWP%) - `game_wins / (game_wins + game_losses)`

## 4. Error Handling Considerations

### 4.1 Pairing Conflicts
- **Repeat pairings**: Query `tournament_pairings` history to avoid
- **Bye conflicts**: Check `tournament_player_stats.byes_received`
- **Odd numbers**: Handle gracefully with bye system
- **Late arrivals**: Allow pairing regeneration before round confirmation

### 4.2 Result Corrections
- **Before next round**: Allow full result editing via `tournament_pairings` updates
- **After tournament**: Require special admin confirmation
- **Cascade updates**: Recalculate `tournament_player_stats` when results change

### 4.3 Data Integrity
- **Transaction safety**: Use Supabase transactions for multi-table updates
- **Validation**: Ensure round progression logic in application
- **Audit trail**: Leverage existing `audit_log` table for changes

### 4.4 Network Issues
- **Auto-save**: Periodically save tournament state
- **Offline handling**: Cache critical data locally using React Query
- **Sync conflicts**: Resolve with timestamp-based precedence

## 5. Security Requirements

### 5.1 Admin-Only Access During Tournament
- **Creation**: Only admins can create tournament organizer (RLS policies)
- **Management**: Only admins can manage active tournaments (leverages existing `is_admin()` function)
- **Result entry**: Only admins can enter/modify results
- **Settings**: Only admins can modify tournament settings

### 5.2 Public Access After Completion
- **View pairings**: Public can view completed round pairings (RLS allows when `status = 'completed'`)
- **View results**: Public can view final standings
- **Historical data**: Tournament data becomes public record

### 5.3 Data Validation
- **Input sanitization**: Validate all user inputs
- **Business rules**: Enforce tournament progression rules
- **Audit logging**: Track all administrative actions using existing audit system

## 6. Integration Points

### 6.1 Existing Components
- **TournamentDetails.tsx**: Main integration point - add button in header area
- **Modal system**: Reuse existing `Modal` component from `@/components/ui/Modal`
- **Color scheme**: Use tournament store colors from `Colors.getStoreScheme()`
- **Authentication**: Leverage existing `useIsAdmin()` hook

### 6.2 Existing Services
- **Supabase client**: Use existing `@/lib/supabase/client`
- **Tournament service**: Extend `@/lib/services/tournaments` for organizer functionality
- **Player service**: Integrate `@/lib/services/players` for participant management
- **Registration service**: Link with existing `@/lib/services/registrationService`

### 6.3 Existing Hooks
- **useIsAdmin**: For permission checking (already implemented)
- **useTournament**: For tournament data (from `@/lib/hooks/useTournaments`)
- **useRegistration**: For player lists (from `@/lib/hooks/useRegistration`)

## 7. Performance Considerations

### 7.1 Real-time Updates
- **Supabase subscriptions**: For live tournament updates using `supabase.channel()`
- **Optimistic updates**: For better user experience with React Query
- **Debouncing**: For frequent operations like timer updates

### 7.2 Data Optimization
- **Pagination**: For large player lists (reuse existing patterns)
- **Caching**: React Query for frequently accessed data
- **Indexing**: Database indexes for pairing queries

### 7.3 Mobile Performance
- **Responsive design**: Ensure mobile usability (follow existing Tailwind patterns)
- **Touch interactions**: Optimize for touch interfaces
- **Offline capability**: Basic functionality without network

## 8. Implementation Steps

### 8.1 Database Setup
1. Create migration file for new tables
2. Apply RLS policies
3. Test admin access patterns

### 8.2 UI Components
1. Add tournament creation button to `TournamentDetails.tsx`
2. Create tournament organizer panel component
3. Implement setup modal and configuration
4. Build pairing interface and timer

### 8.3 Business Logic
1. Implement Swiss pairing algorithm
2. Create round management system
3. Build scoring and statistics calculation
4. Add result entry and correction features

### 8.4 Integration
1. Connect to existing tournament system
2. Integrate with player registration
3. Add real-time updates
4. Implement security checks

## 9. Future Enhancements

### 9.1 Advanced Features
- **Custom pairing rules**: Store-specific modifications
- **Multiple formats**: Support for different tournament types (Single Elimination, Round Robin)
- **Export functionality**: PDF reports, CSV data export
- **Integration**: With external tournament software (MTG Melee, etc.)

### 9.2 Analytics
- **Tournament metrics**: Performance analytics dashboard
- **Player statistics**: Historical performance tracking across tournaments
- **Reporting**: Automated tournament reports for store owners

This planning document provides a comprehensive foundation for implementing the Tournament Organizer feature while maintaining consistency with the existing codebase architecture, leveraging the current Supabase setup, and following established design patterns.
