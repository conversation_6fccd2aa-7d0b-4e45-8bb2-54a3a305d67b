#!/usr/bin/env ts-node

/**
 * Secure Admin User Creation Script
 * 
 * This script creates an admin user by:
 * 1. Validating environment variables
 * 2. Prompting for email input with validation
 * 3. Creating the user via Supabase Auth
 * 4. Assigning admin role in the database
 * 
 * Usage: npm run create-admin
 */

import { createClient } from '@supabase/supabase-js';
import * as readline from 'readline';
import { config } from 'dotenv';
import { randomUUID } from 'crypto';

// Load environment variables
config({ path: '.env.local' });

interface AdminCreationResult {
  success: boolean;
  message: string;
  userId?: string;
}

/**
 * Validates environment variables required for admin creation
 */
function validateEnvironment(): { isValid: boolean; error?: string } {
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ];

  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      return {
        isValid: false,
        error: `Missing required environment variable: ${varName}`
      };
    }
  }

  // Validate Supabase URL format
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  if (!supabaseUrl.startsWith('https://') || !supabaseUrl.includes('.supabase.co')) {
    return {
      isValid: false,
      error: 'Invalid NEXT_PUBLIC_SUPABASE_URL format'
    };
  }

  return { isValid: true };
}

/**
 * Validates email format
 */
function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
}

/**
 * Prompts user for email input with validation
 */
function promptForEmail(): Promise<string> {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve, reject) => {
    const askEmail = () => {
      rl.question('Enter admin email address: ', (email) => {
        const trimmedEmail = email.trim();
        
        if (!trimmedEmail) {
          console.log('❌ Email cannot be empty. Please try again.');
          askEmail();
          return;
        }

        if (!validateEmail(trimmedEmail)) {
          console.log('❌ Invalid email format. Please try again.');
          askEmail();
          return;
        }

        rl.close();
        resolve(trimmedEmail);
      });
    };

    askEmail();
  });
}

/**
 * Creates admin user and assigns role
 */
async function createAdminUser(email: string): Promise<AdminCreationResult> {
  try {
    // Initialize Supabase client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    console.log('🔄 Creating admin user...');

    // Since we're using the anon key, we'll use the sign-up flow
    // This will create the user and send them a magic link
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: email,
      password: randomUUID(), // Generate a random password (won't be used due to magic links)
      options: {
        data: {
          role: 'admin',
          created_by: 'admin-script'
        }
      }
    });

    if (authError) {
      return {
        success: false,
        message: `Failed to create user: ${authError.message}`
      };
    }

    if (!authData.user) {
      return {
        success: false,
        message: 'User creation failed: No user data returned'
      };
    }

    const userId = authData.user.id;
    console.log(`✅ User created with ID: ${userId}`);

    // Wait a moment for the user to be fully created
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Assign admin role in database
    console.log('🔄 Assigning admin role...');

    const { error: roleError } = await supabase
      .from('user_roles')
      .insert({
        user_id: userId,
        role: 'admin'
      });

    if (roleError) {
      console.log('⚠️ Role assignment failed. The user was created but admin role assignment failed.');
      console.log('You can manually assign the admin role using the following SQL:');
      console.log(`INSERT INTO public.user_roles (user_id, role) VALUES ('${userId}', 'admin');`);

      return {
        success: false,
        message: `User created but role assignment failed: ${roleError.message}. See console for manual fix.`
      };
    }

    return {
      success: true,
      message: 'Admin user created successfully',
      userId: userId
    };

  } catch (error) {
    return {
      success: false,
      message: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Admin User Creation Script');
  console.log('================================\n');

  // Validate environment
  const envValidation = validateEnvironment();
  if (!envValidation.isValid) {
    console.error(`❌ Environment validation failed: ${envValidation.error}`);
    process.exit(1);
  }

  console.log('✅ Environment variables validated');

  try {
    // Get email from user
    const email = await promptForEmail();
    console.log(`📧 Creating admin user for: ${email}\n`);

    // Create admin user
    const result = await createAdminUser(email);

    if (result.success) {
      console.log('\n🎉 SUCCESS!');
      console.log(`✅ ${result.message}`);
      console.log(`📧 Admin user: ${email}`);
      if (result.userId) {
        console.log(`🆔 User ID: ${result.userId}`);
      }
      console.log('\n📝 Next steps:');
      console.log('1. The user will receive a magic link email to set up their account');
      console.log('2. They can then access admin features at /admin/dashboard');
    } else {
      console.error('\n❌ FAILED!');
      console.error(`Error: ${result.message}`);
      process.exit(1);
    }

  } catch (error) {
    console.error('\n❌ Script execution failed:');
    console.error(error instanceof Error ? error.message : 'Unknown error');
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  main().catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}
