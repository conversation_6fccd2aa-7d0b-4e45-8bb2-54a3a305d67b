import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { withRateLimit, rateLimiters } from '@/lib/middleware/rateLimit';
import { createErrorResponse } from '@/lib/utils/errorSanitizer';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function handler(_request: NextRequest) {
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    const errorResponse = createErrorResponse(
      'Missing Supabase configuration',
      'SERVICE_UNAVAILABLE'
    );
    return NextResponse.json(errorResponse, { status: 500 });
  }

  const cookieStore = await cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            cookieStore.set(name, value, options);
          });
        },
      },
    }
  );

  const { data: userRes } = await supabase.auth.getUser();
  if (!userRes.user) {
    return NextResponse.json({ isAdmin: false }, { status: 200, headers: { 'Cache-Control': 'private, max-age=60' } });
  }

  try {
    const { data } = await supabase.rpc('is_admin');
    return NextResponse.json(
      { isAdmin: Boolean(data) },
      { status: 200, headers: { 'Cache-Control': 'no-store', 'Vary': 'Cookie' } }
    );
  } catch {
    return NextResponse.json(
      { isAdmin: false },
      { status: 200, headers: { 'Cache-Control': 'no-store', 'Vary': 'Cookie' } }
    );
  }
}

// Apply rate limiting to the GET handler
export const GET = withRateLimit(handler, rateLimiters.auth);
