import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { RegistrationService, CreateRegistrationParams, AdminCreatePlayerRegistrationParams } from '../services/registrationService';

/**
 * Hook to create a tournament registration
 */
export function useCreateRegistration() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: CreateRegistrationParams) => {
      return RegistrationService.createRegistration(params);
    },
    onSuccess: (data, variables) => {
      // Invalidate user's personal registrations list (for profile page)
      queryClient.invalidateQueries({
        queryKey: ['my-registrations']
      });
      
      // Invalidate registration-specific queries
      queryClient.invalidateQueries({
        queryKey: ['tournament-registration-status', variables.tournamentId]
      });
      queryClient.invalidateQueries({
        queryKey: ['user-registration', variables.tournamentId]
      });
      queryClient.invalidateQueries({
        queryKey: ['tournament-registrations', variables.tournamentId]
      });
      
      // Invalidate tournament queries to update player counts
      queryClient.invalidateQueries({
        queryKey: ['tournaments', variables.tournamentId]
      });
      
      // Also invalidate general tournament queries for lists/calendar views
      queryClient.invalidateQueries({ queryKey: ['tournaments'] });
      queryClient.invalidateQueries({ queryKey: ['tournaments', 'month'] });
      queryClient.invalidateQueries({ queryKey: ['tournaments', 'date'] });
      queryClient.invalidateQueries({ queryKey: ['tournaments', 'paginated'] });
    },
    onError: (error) => {
      console.error('Registration creation failed:', error);
    },
  });
}

/**
 * Hook to check if user is registered for a tournament
 */
export function useRegistrationStatus(tournamentId: string) {
  return useQuery({
    queryKey: ['tournament-registration-status', tournamentId],
    queryFn: () => RegistrationService.checkRegistrationStatus(tournamentId),
    enabled: !!tournamentId,
    staleTime: 1000 * 60 * 5, // Consider data fresh for 5 minutes
    gcTime: 1000 * 60 * 10, // Keep in cache for 10 minutes
  });
}

/**
 * Hook to get user's registration for a specific tournament
 */
export function useUserRegistration(tournamentId: string) {
  return useQuery({
    queryKey: ['user-registration', tournamentId],
    queryFn: () => RegistrationService.getUserRegistration(tournamentId),
    enabled: !!tournamentId,
    staleTime: 1000 * 60 * 5, // Consider data fresh for 5 minutes
    gcTime: 1000 * 60 * 10, // Keep in cache for 10 minutes
  });
}

/**
 * Hook to get all registrations for a tournament
 * Intelligently determines whether to include sensitive deck data based on tournament date and user role
 */
export function useTournamentRegistrations(tournamentId: string, tournament?: { date: string }, isAdmin?: boolean) {
  // Determine if we should include sensitive deck data (admins always get full data)
  // Use consistent date-only logic: only tournaments from yesterday and earlier are considered "past"
  let includeDeckData: boolean = isAdmin === true; // Default to false if no tournament data or user is not admin
  
  if (tournament) {
    const tournamentDate = new Date(tournament.date);
    const today = new Date();
    
    // Set today to start of day (00:00:00) for comparison
    const todayStartOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    
    // Set tournament date to start of day (00:00:00) for comparison
    const tournamentStartOfDay = new Date(tournamentDate.getFullYear(), tournamentDate.getMonth(), tournamentDate.getDate());
    
    // Only show sensitive deck data for tournaments from yesterday and earlier
    includeDeckData = isAdmin || (tournamentStartOfDay < todayStartOfDay);
  }
  
  return useQuery({
    queryKey: ['tournament-registrations', tournamentId, includeDeckData],
    queryFn: () => RegistrationService.getTournamentRegistrations(tournamentId, includeDeckData),
    enabled: !!tournamentId,
    staleTime: 1000 * 60 * 2, // Consider data fresh for 2 minutes
    gcTime: 1000 * 60 * 10, // Keep in cache for 10 minutes
  });
}

/**
 * Hook to update a user's registration
 */
export function useUpdateRegistration() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: RegistrationService.updateRegistration,
    onSuccess: async (data) => {
      // Force immediate invalidation and refetch of all related queries
      await Promise.all([
        // Invalidate and refetch user registrations (for profile page)
        queryClient.invalidateQueries({ queryKey: ['my-registrations'] }),
        queryClient.refetchQueries({ queryKey: ['my-registrations'] }),

        // Invalidate tournament registrations for this specific tournament (for admin/public lists)
        queryClient.invalidateQueries({
          queryKey: ['tournament-registrations', data.tournament_id],
          exact: false
        }),

        // Invalidate user registration for this tournament (for manage modal)
        queryClient.invalidateQueries({
          queryKey: ['user-registration', data.tournament_id]
        }),
        queryClient.refetchQueries({
          queryKey: ['user-registration', data.tournament_id]
        }),

        // Invalidate registration status for this tournament (for buttons)
        queryClient.invalidateQueries({
          queryKey: ['registration-status', data.tournament_id]
        }),
        queryClient.refetchQueries({
          queryKey: ['registration-status', data.tournament_id]
        }),

        // Invalidate tournament data to refresh participant count
        queryClient.invalidateQueries({
          queryKey: ['tournaments', data.tournament_id]
        }),
        queryClient.refetchQueries({
          queryKey: ['tournaments', data.tournament_id]
        })
      ]);

      // Additional aggressive cache clearing
      queryClient.removeQueries({ queryKey: ['my-registrations'] });
      queryClient.removeQueries({ queryKey: ['user-registration', data.tournament_id] });
    },
  });
}

/**
 * Hook to get the count of registrations for a tournament
 */
export function useTournamentRegistrationCount(tournamentId: string) {
  const { data: registrations, isLoading, error } = useTournamentRegistrations(tournamentId);

  return {
    count: registrations?.length || 0,
    isLoading,
    error
  };
}

/**
 * Hook to create a player registration as admin
 */
export function useAdminCreatePlayerRegistration() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: AdminCreatePlayerRegistrationParams) =>
      RegistrationService.adminCreatePlayerRegistration(params),
    onSuccess: (_, variables) => {
      // Invalidate tournament registrations to refresh the list
      queryClient.invalidateQueries({
        queryKey: ['tournament-registrations', variables.tournamentId]
      });
      // Also invalidate tournament data to update player count
      queryClient.invalidateQueries({
        queryKey: ['tournaments', variables.tournamentId]
      });
    }
  });
}

/**
 * Hook to get a specific registration as admin
 */
export function useAdminRegistration(registrationId: string) {
  return useQuery({
    queryKey: ['admin-registration', registrationId],
    queryFn: () => RegistrationService.adminGetRegistration(registrationId),
    enabled: !!registrationId,
  });
}

/**
 * Hook to update any registration as admin
 */
export function useAdminUpdateRegistration() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: {
      registrationId: string;
      deckName?: string | null;
      archetypeId?: string | null;
      deckListUrl?: string | null;
    }) => RegistrationService.adminUpdateRegistration(params),
    onSuccess: (data) => {
      // Invalidate specific registration
      queryClient.invalidateQueries({
        queryKey: ['admin-registration', data.id]
      });
      // Invalidate tournament registrations to refresh the list
      queryClient.invalidateQueries({
        queryKey: ['tournament-registrations', data.tournament_id]
      });
      // Also invalidate tournament data
      queryClient.invalidateQueries({
        queryKey: ['tournaments', data.tournament_id]
      });
    }
  });
}

/**
 * Hook to delete any registration as admin
 */
export function useAdminDeleteRegistration() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (registrationId: string) =>
      RegistrationService.adminDeleteRegistration(registrationId),
    onSuccess: (data) => {
      // Invalidate tournament registrations to refresh the list
      queryClient.invalidateQueries({
        queryKey: ['tournament-registrations', data.tournamentId]
      });
      // Also invalidate tournament data to update player count
      queryClient.invalidateQueries({
        queryKey: ['tournaments', data.tournamentId]
      });
    }
  });
}
