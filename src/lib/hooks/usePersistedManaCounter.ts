import { useState, useEffect } from 'react';

export type ManaType = "white" | "blue" | "black" | "red" | "green" | "colorless";

const MANA_COUNTER_STORAGE_KEY = 'lpa-mana-counter-state';
const MANA_VISIBLE_BARS_STORAGE_KEY = 'lpa-mana-counter-visible-bars';

const DEFAULT_MANA_COUNT: Record<ManaType, number> = {
  white: 0,
  blue: 0,
  black: 0,
  red: 0,
  green: 0,
  colorless: 0,
};

const DEFAULT_VISIBLE_BARS: Set<ManaType> = new Set(["white", "blue", "black", "red", "green"]);

export function usePersistedManaCounter() {
  const [manaCount, setManaCount] = useState<Record<ManaType, number>>(DEFAULT_MANA_COUNT);
  const [visibleBars, setVisibleBars] = useState<Set<ManaType>>(DEFAULT_VISIBLE_BARS);
  const [isLoaded, setIsLoaded] = useState(false);

  // Carica lo stato dal localStorage al mount
  useEffect(() => {
    try {
      // Carica i contatori di mana
      const savedManaCount = localStorage.getItem(MANA_COUNTER_STORAGE_KEY);
      if (savedManaCount) {
        const parsedManaCount: Record<ManaType, number> = JSON.parse(savedManaCount);
        setManaCount(parsedManaCount);
      }

      // Carica le barre visibili
      const savedVisibleBars = localStorage.getItem(MANA_VISIBLE_BARS_STORAGE_KEY);
      if (savedVisibleBars) {
        const parsedVisibleBars: ManaType[] = JSON.parse(savedVisibleBars);
        setVisibleBars(new Set(parsedVisibleBars));
      }
    } catch (error) {
      console.error('Errore nel caricamento dello stato del Mana Counter:', error);
      // In caso di errore, usa i valori di default
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Salva i contatori di mana nel localStorage ogni volta che cambiano
  useEffect(() => {
    if (!isLoaded) return; // Non salvare durante il caricamento iniziale
    
    try {
      localStorage.setItem(MANA_COUNTER_STORAGE_KEY, JSON.stringify(manaCount));
    } catch (error) {
      console.error('Errore nel salvataggio dei contatori di mana:', error);
    }
  }, [manaCount, isLoaded]);

  // Salva le barre visibili nel localStorage ogni volta che cambiano
  useEffect(() => {
    if (!isLoaded) return; // Non salvare durante il caricamento iniziale
    
    try {
      const visibleBarsArray = Array.from(visibleBars);
      localStorage.setItem(MANA_VISIBLE_BARS_STORAGE_KEY, JSON.stringify(visibleBarsArray));
    } catch (error) {
      console.error('Errore nel salvataggio delle barre visibili:', error);
    }
  }, [visibleBars, isLoaded]);

  const handleManaChange = (type: ManaType, amount: number) => {
    setManaCount(prev => ({
      ...prev,
      [type]: Math.max(0, prev[type] + amount),
    }));
  };

  const resetAllMana = () => {
    setManaCount({ ...DEFAULT_MANA_COUNT });
  };

  const toggleBarVisibility = (type: ManaType) => {
    setVisibleBars(prev => {
      const newSet = new Set(prev);
      if (newSet.has(type)) {
        newSet.delete(type);
      } else {
        newSet.add(type);
      }
      return newSet;
    });
  };

  return {
    manaCount,
    visibleBars,
    handleManaChange,
    resetAllMana,
    toggleBarVisibility,
    isLoaded,
  };
}
