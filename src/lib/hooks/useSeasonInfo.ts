import { useQuery } from '@tanstack/react-query';
import { supabase } from '../supabase/client';
import { logger } from '../utils/logger';

interface SeasonInfo {
  name: string | null;
  byeStatus: {
    achieved: boolean;
    average: number;
  };
}

interface TournamentWithResults {
  id: string;
  title: string;
  date: string;
  resultCount: number;
}

/**
 * Hook to get active season info and bye calculation
 */
export function useSeasonInfo() {
  return useQuery<SeasonInfo>({
    queryKey: ['season-info'],
    queryFn: async () => {
      try {
        // 1. Get active season
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const { data: activeSeason, error: seasonError } = await (supabase as any)
          .from('seasons')
          .select('id, name')
          .eq('is_active', true)
          .maybeSingle();

        if (seasonError && seasonError.code !== 'PGRST116') {
          logger.error('Error fetching active season', seasonError, { component: 'useSeasonInfo' });
        }

        // 2. If no active season, return default
        if (!activeSeason) {
          return {
            name: null,
            byeStatus: {
              achieved: false,
              average: 0
            }
          };
        }

        // 3. Get all "Tappa di lega" tournaments from active season with their result counts
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const { data: tournaments, error: tournamentsError } = await (supabase as any)
          .from('tournaments')
          .select('id, title, date, season_id')
          .eq('title', 'Tappa di lega')
          .eq('season_id', activeSeason.id)
          .order('date', { ascending: false });

        if (tournamentsError) {
          logger.error('Error fetching tournaments', tournamentsError, { component: 'useSeasonInfo' });
          throw tournamentsError;
        }

        if (!tournaments || tournaments.length === 0) {
          return {
            name: activeSeason.name,
            byeStatus: {
              achieved: false,
              average: 0
            }
          };
        }

        // 4. Get result counts for each tournament
        const tournamentIds = tournaments.map((t: { id: string }) => t.id);
        
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const { data: resultCounts, error: resultsError } = await (supabase as any)
          .from('tournament_results')
          .select('tournament_id')
          .in('tournament_id', tournamentIds);

        if (resultsError) {
          logger.error('Error fetching tournament results', resultsError, { component: 'useSeasonInfo' });
          throw resultsError;
        }

        // 5. Count results per tournament
        const resultCountMap = new Map<string, number>();
        if (resultCounts) {
          resultCounts.forEach((result: { tournament_id: string }) => {
            const count = resultCountMap.get(result.tournament_id) || 0;
            resultCountMap.set(result.tournament_id, count + 1);
          });
        }

        // 6. Create tournaments with result counts
        const tournamentsWithResults: TournamentWithResults[] = tournaments.map((tournament: { id: string; title: string; date: string }) => ({
          id: tournament.id,
          title: tournament.title,
          date: tournament.date,
          resultCount: resultCountMap.get(tournament.id) || 0
        }));

        // 7. Filter only tournaments with results (completed tournaments)
        const completedTournaments = tournamentsWithResults.filter(t => t.resultCount > 0);

        // 8. Sort by participant count (descending) and take top 6
        const top6Tournaments = completedTournaments
          .sort((a, b) => b.resultCount - a.resultCount)
          .slice(0, 6);

        // 9. Calculate average
        const totalParticipants = top6Tournaments.reduce((sum, tournament) => sum + tournament.resultCount, 0);
        const average = top6Tournaments.length > 0 ? Math.floor(totalParticipants / top6Tournaments.length) : 0;

        // 10. Check if bye is achieved (average >= 16)
        const achieved = average >= 16;

        logger.info('Season info calculated', {
          seasonName: activeSeason.name,
          totalTournaments: tournaments.length,
          completedTournaments: completedTournaments.length,
          top6Count: top6Tournaments.length,
          totalParticipants,
          average,
          achieved
        });

        return {
          name: activeSeason.name,
          byeStatus: {
            achieved,
            average
          }
        };

      } catch (error) {
        logger.error('Error in useSeasonInfo', error, { component: 'useSeasonInfo' });
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}
