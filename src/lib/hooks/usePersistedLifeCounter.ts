import { useState, useEffect } from 'react';

const LIFE_COUNTER_STORAGE_KEY = 'lpa-life-counter-state';

interface LifeCounterState {
  player1Life: number;
  player2Life: number;
}

const DEFAULT_STATE: LifeCounterState = {
  player1Life: 20,
  player2Life: 20,
};

export function usePersistedLifeCounter() {
  const [player1Life, setPlayer1Life] = useState(DEFAULT_STATE.player1Life);
  const [player2Life, setPlayer2Life] = useState(DEFAULT_STATE.player2Life);
  const [isLoaded, setIsLoaded] = useState(false);

  // Carica lo stato dal localStorage al mount
  useEffect(() => {
    try {
      const savedState = localStorage.getItem(LIFE_COUNTER_STORAGE_KEY);
      if (savedState) {
        const parsedState: LifeCounterState = JSON.parse(savedState);
        setPlayer1Life(parsedState.player1Life);
        setPlayer2Life(parsedState.player2Life);
      }
    } catch (error) {
      console.error('Errore nel caricamento dello stato del Life Counter:', error);
      // In caso di errore, usa i valori di default
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Salva lo stato nel localStorage ogni volta che cambia
  useEffect(() => {
    if (!isLoaded) return; // Non salvare durante il caricamento iniziale
    
    try {
      const stateToSave: LifeCounterState = {
        player1Life,
        player2Life,
      };
      localStorage.setItem(LIFE_COUNTER_STORAGE_KEY, JSON.stringify(stateToSave));
    } catch (error) {
      console.error('Errore nel salvataggio dello stato del Life Counter:', error);
    }
  }, [player1Life, player2Life, isLoaded]);

  const handleLifeChange = (player: 1 | 2, amount: number) => {
    if (player === 1) {
      setPlayer1Life(prev => prev + amount);
    } else {
      setPlayer2Life(prev => prev + amount);
    }
  };

  const resetLife = () => {
    setPlayer1Life(DEFAULT_STATE.player1Life);
    setPlayer2Life(DEFAULT_STATE.player2Life);
  };

  return {
    player1Life,
    player2Life,
    handleLifeChange,
    resetLife,
    isLoaded,
  };
}
