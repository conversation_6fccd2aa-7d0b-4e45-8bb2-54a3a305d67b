/**
 * Round Management System
 * 
 * This module handles tournament round progression, state transitions,
 * timer integration, and round validation for the tournament organizer.
 */

import {
  TournamentOrganizerSettings,
  TournamentRound,
  PairingWithPlayers,
  TournamentPlayerStats
} from '@/lib/mock/tournamentOrganizerMock';

export type RoundStatus = 'pending' | 'active' | 'completed';
export type TournamentStatus = 'setup' | 'active' | 'paused' | 'completed';

export interface RoundTransitionResult {
  success: boolean;
  error?: string;
  newStatus?: RoundStatus;
  newTournamentStatus?: TournamentStatus;
}

export interface RoundValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface TimerState {
  isRunning: boolean;
  timeRemaining: number; // in seconds
  totalTime: number; // in seconds
  startedAt?: Date;
  pausedAt?: Date;
}

/**
 * Round Management Service
 */
export class RoundManager {
  private static timers: Map<string, TimerState> = new Map();

  /**
   * Start a new round
   */
  static async startRound(
    roundId: string,
    settings: TournamentOrganizerSettings,
    pairings: PairingWithPlayers[]
  ): Promise<RoundTransitionResult> {
    try {
      // Validate that round can be started
      const validation = this.validateRoundStart(pairings);
      if (!validation.isValid) {
        return {
          success: false,
          error: `Cannot start round: ${validation.errors.join(', ')}`
        };
      }

      // Initialize timer for this round
      const totalTime = settings.round_duration_minutes * 60;
      this.timers.set(roundId, {
        isRunning: true,
        timeRemaining: totalTime,
        totalTime,
        startedAt: new Date()
      });

      return {
        success: true,
        newStatus: 'active',
        newTournamentStatus: 'active'
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to start round: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Complete a round
   */
  static async completeRound(
    roundId: string,
    pairings: PairingWithPlayers[],
    settings: TournamentOrganizerSettings
  ): Promise<RoundTransitionResult> {
    try {
      // Validate that round can be completed
      const validation = this.validateRoundCompletion(pairings);
      if (!validation.isValid) {
        return {
          success: false,
          error: `Cannot complete round: ${validation.errors.join(', ')}`
        };
      }

      // Stop timer for this round
      const timer = this.timers.get(roundId);
      if (timer) {
        timer.isRunning = false;
        this.timers.set(roundId, timer);
      }

      // Determine if tournament is complete
      const isLastRound = settings.current_round >= settings.rounds_total;
      const newTournamentStatus: TournamentStatus = isLastRound ? 'completed' : 'active';

      return {
        success: true,
        newStatus: 'completed',
        newTournamentStatus
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to complete round: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Pause a round
   */
  static pauseRound(roundId: string): RoundTransitionResult {
    try {
      const timer = this.timers.get(roundId);
      if (!timer) {
        return { success: false, error: 'Round timer not found' };
      }

      if (!timer.isRunning) {
        return { success: false, error: 'Round is not currently running' };
      }

      timer.isRunning = false;
      timer.pausedAt = new Date();
      this.timers.set(roundId, timer);

      return {
        success: true,
        newTournamentStatus: 'paused'
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to pause round: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Resume a paused round
   */
  static resumeRound(roundId: string): RoundTransitionResult {
    try {
      const timer = this.timers.get(roundId);
      if (!timer) {
        return { success: false, error: 'Round timer not found' };
      }

      if (timer.isRunning) {
        return { success: false, error: 'Round is already running' };
      }

      timer.isRunning = true;
      timer.pausedAt = undefined;
      this.timers.set(roundId, timer);

      return {
        success: true,
        newTournamentStatus: 'active'
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to resume round: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Get timer state for a round
   */
  static getTimerState(roundId: string): TimerState | null {
    return this.timers.get(roundId) || null;
  }

  /**
   * Update timer for a round (called periodically)
   */
  static updateTimer(roundId: string): TimerState | null {
    const timer = this.timers.get(roundId);
    if (!timer || !timer.isRunning || !timer.startedAt) {
      return timer || null;
    }

    const now = new Date();
    const elapsedSeconds = Math.floor((now.getTime() - timer.startedAt.getTime()) / 1000);
    const timeRemaining = Math.max(0, timer.totalTime - elapsedSeconds);

    const updatedTimer = {
      ...timer,
      timeRemaining
    };

    this.timers.set(roundId, updatedTimer);
    return updatedTimer;
  }

  /**
   * Reset timer for a round
   */
  static resetTimer(roundId: string, durationMinutes: number): RoundTransitionResult {
    try {
      const totalTime = durationMinutes * 60;
      this.timers.set(roundId, {
        isRunning: false,
        timeRemaining: totalTime,
        totalTime
      });

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: `Failed to reset timer: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Validate that a round can be started
   */
  static validateRoundStart(pairings: PairingWithPlayers[]): RoundValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (pairings.length === 0) {
      errors.push('No pairings generated for this round');
    }

    // Check for duplicate players
    const playerIds = new Set<string>();
    pairings.forEach(pairing => {
      if (playerIds.has(pairing.player1_id)) {
        errors.push(`Player ${pairing.player1.first_name} ${pairing.player1.last_name} appears in multiple pairings`);
      }
      playerIds.add(pairing.player1_id);

      if (pairing.player2_id) {
        if (playerIds.has(pairing.player2_id)) {
          errors.push(`Player ${pairing.player2?.first_name} ${pairing.player2?.last_name} appears in multiple pairings`);
        }
        playerIds.add(pairing.player2_id);
      }
    });

    // Check for missing table numbers
    const regularPairings = pairings.filter(p => !p.is_bye);
    const tableNumbers = regularPairings.map(p => p.table_number).filter(Boolean);
    if (tableNumbers.length !== regularPairings.length) {
      warnings.push('Some pairings are missing table numbers');
    }

    // Check for duplicate table numbers
    const uniqueTableNumbers = new Set(tableNumbers);
    if (uniqueTableNumbers.size !== tableNumbers.length) {
      errors.push('Duplicate table numbers found');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate that a round can be completed
   */
  static validateRoundCompletion(pairings: PairingWithPlayers[]): RoundValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (pairings.length === 0) {
      errors.push('No pairings found for this round');
      return { isValid: false, errors, warnings };
    }

    // Check that all matches have results
    const matchesWithoutResults = pairings.filter(pairing => {
      if (pairing.is_bye) {
        return false; // Byes don't need results
      }
      return !pairing.result;
    });

    if (matchesWithoutResults.length > 0) {
      errors.push(`${matchesWithoutResults.length} matches are missing results`);
    }

    // Validate result formats
    const validResults = ['2-0', '2-1', '1-2', '0-2', '1-0', '0-1', '0-0'];
    pairings.forEach(pairing => {
      if (!pairing.is_bye && pairing.result && !validResults.includes(pairing.result)) {
        errors.push(`Invalid result format: ${pairing.result}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Calculate round progress percentage
   */
  static calculateRoundProgress(pairings: PairingWithPlayers[]): number {
    if (pairings.length === 0) return 0;

    const completedMatches = pairings.filter(pairing => {
      return pairing.is_bye || pairing.result;
    }).length;

    return Math.round((completedMatches / pairings.length) * 100);
  }

  /**
   * Get round summary statistics
   */
  static getRoundSummary(pairings: PairingWithPlayers[]): {
    totalMatches: number;
    completedMatches: number;
    pendingMatches: number;
    byeMatches: number;
    progressPercentage: number;
  } {
    const totalMatches = pairings.length;
    const byeMatches = pairings.filter(p => p.is_bye).length;
    const completedMatches = pairings.filter(p => p.is_bye || p.result).length;
    const pendingMatches = totalMatches - completedMatches;
    const progressPercentage = this.calculateRoundProgress(pairings);

    return {
      totalMatches,
      completedMatches,
      pendingMatches,
      byeMatches,
      progressPercentage
    };
  }

  /**
   * Clean up timer for a round (call when round is deleted or tournament ends)
   */
  static cleanupTimer(roundId: string): void {
    this.timers.delete(roundId);
  }

  /**
   * Clean up all timers (call when tournament ends)
   */
  static cleanupAllTimers(): void {
    this.timers.clear();
  }

  /**
   * Get all active timers (for debugging)
   */
  static getAllTimers(): Map<string, TimerState> {
    return new Map(this.timers);
  }
}
