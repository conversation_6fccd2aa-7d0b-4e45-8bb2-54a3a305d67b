/**
 * Tournament Scoring System
 * 
 * This module implements the Magic: The Gathering tournament scoring system:
 * - Match Points: 3 for win, 1 for draw, 0 for loss
 * - Game Win Percentage (GWP): (Games Won) / (Games Played)
 * - Opponent Match Win Percentage (OMW%): Average of opponents' match win percentages
 * 
 * Tiebreakers (in order):
 * 1. Match Points
 * 2. Opponent Match Win Percentage (OMW%)
 * 3. Game Win Percentage (GWP%)
 * 4. Opponent Game Win Percentage (OGW%)
 */

import {
  PairingWithPlayers,
  TournamentPlayerStats,
  PlayerWithStats
} from '@/lib/mock/tournamentOrganizerMock';

export interface MatchResult {
  player1_id: string;
  player2_id?: string;
  result: string; // '2-0', '2-1', '1-2', '0-2', '1-0', '0-1', '0-0'
  is_bye: boolean;
}

export interface PlayerMatchData {
  playerId: string;
  matchPoints: number;
  gameWins: number;
  gameLosses: number;
  matchesPlayed: number;
  byes: number;
  opponents: string[];
}

export interface TiebreakersData {
  omwPercentage: number;
  gwpPercentage: number;
  ogwPercentage: number;
}

export interface PlayerStanding extends PlayerMatchData, TiebreakersData {
  position: number;
}

/**
 * Tournament Scoring Calculator
 */
export class TournamentScoring {
  /**
   * Calculate match points from a result string
   */
  static calculateMatchPoints(result: string, playerId: string, player1Id: string): number {
    if (result === 'bye') return 3; // Bye gives 3 points

    const isPlayer1 = playerId === player1Id;
    
    switch (result) {
      case '2-0':
        return isPlayer1 ? 3 : 0;
      case '2-1':
        return isPlayer1 ? 3 : 0;
      case '1-2':
        return isPlayer1 ? 0 : 3;
      case '0-2':
        return isPlayer1 ? 0 : 3;
      case '1-0':
        return isPlayer1 ? 3 : 0;
      case '0-1':
        return isPlayer1 ? 0 : 3;
      case '0-0':
        return 1; // Draw gives 1 point to both players
      default:
        return 0;
    }
  }

  /**
   * Calculate game wins and losses from a result string
   */
  static calculateGameRecord(result: string, playerId: string, player1Id: string): { wins: number; losses: number } {
    if (result === 'bye') return { wins: 2, losses: 0 }; // Bye counts as 2-0

    const isPlayer1 = playerId === player1Id;
    
    switch (result) {
      case '2-0':
        return isPlayer1 ? { wins: 2, losses: 0 } : { wins: 0, losses: 2 };
      case '2-1':
        return isPlayer1 ? { wins: 2, losses: 1 } : { wins: 1, losses: 2 };
      case '1-2':
        return isPlayer1 ? { wins: 1, losses: 2 } : { wins: 2, losses: 1 };
      case '0-2':
        return isPlayer1 ? { wins: 0, losses: 2 } : { wins: 2, losses: 0 };
      case '1-0':
        return isPlayer1 ? { wins: 1, losses: 0 } : { wins: 0, losses: 1 };
      case '0-1':
        return isPlayer1 ? { wins: 0, losses: 1 } : { wins: 1, losses: 0 };
      case '0-0':
        return { wins: 0, losses: 0 }; // Draw
      default:
        return { wins: 0, losses: 0 };
    }
  }

  /**
   * Process all match results and calculate player statistics
   */
  static calculatePlayerStats(
    players: PlayerWithStats[],
    allPairings: PairingWithPlayers[]
  ): Map<string, PlayerMatchData> {
    const playerStats = new Map<string, PlayerMatchData>();

    // Initialize player stats
    players.forEach(player => {
      playerStats.set(player.id, {
        playerId: player.id,
        matchPoints: 0,
        gameWins: 0,
        gameLosses: 0,
        matchesPlayed: 0,
        byes: 0,
        opponents: []
      });
    });

    // Process each pairing with results
    allPairings.forEach(pairing => {
      if (!pairing.result) return; // Skip pairings without results

      const player1Stats = playerStats.get(pairing.player1_id);
      if (!player1Stats) return;

      if (pairing.is_bye) {
        // Handle bye
        player1Stats.matchPoints += 3;
        player1Stats.gameWins += 2;
        player1Stats.matchesPlayed += 1;
        player1Stats.byes += 1;
      } else if (pairing.player2_id) {
        // Handle regular match
        const player2Stats = playerStats.get(pairing.player2_id);
        if (!player2Stats) return;

        // Calculate points and game record for both players
        const player1Points = this.calculateMatchPoints(pairing.result, pairing.player1_id, pairing.player1_id);
        const player2Points = this.calculateMatchPoints(pairing.result, pairing.player2_id, pairing.player1_id);

        const player1Games = this.calculateGameRecord(pairing.result, pairing.player1_id, pairing.player1_id);
        const player2Games = this.calculateGameRecord(pairing.result, pairing.player2_id, pairing.player1_id);

        // Update player 1 stats
        player1Stats.matchPoints += player1Points;
        player1Stats.gameWins += player1Games.wins;
        player1Stats.gameLosses += player1Games.losses;
        player1Stats.matchesPlayed += 1;
        player1Stats.opponents.push(pairing.player2_id);

        // Update player 2 stats
        player2Stats.matchPoints += player2Points;
        player2Stats.gameWins += player2Games.wins;
        player2Stats.gameLosses += player2Games.losses;
        player2Stats.matchesPlayed += 1;
        player2Stats.opponents.push(pairing.player1_id);
      }
    });

    return playerStats;
  }

  /**
   * Calculate Game Win Percentage (GWP)
   * Minimum 33% for tiebreaker calculations
   */
  static calculateGWP(gameWins: number, gameLosses: number): number {
    const totalGames = gameWins + gameLosses;
    if (totalGames === 0) return 0.33; // Minimum 33%
    
    const gwp = gameWins / totalGames;
    return Math.max(gwp, 0.33); // Minimum 33% per MTG rules
  }

  /**
   * Calculate Match Win Percentage (MWP)
   * Used for OMW% calculations
   */
  static calculateMWP(matchPoints: number, matchesPlayed: number): number {
    if (matchesPlayed === 0) return 0.33; // Minimum 33%
    
    const mwp = matchPoints / (matchesPlayed * 3); // 3 points per match
    return Math.max(mwp, 0.33); // Minimum 33% per MTG rules
  }

  /**
   * Calculate Opponent Match Win Percentage (OMW%)
   */
  static calculateOMW(
    playerOpponents: string[],
    allPlayerStats: Map<string, PlayerMatchData>
  ): number {
    if (playerOpponents.length === 0) return 0.33; // Minimum 33%

    let totalMWP = 0;
    let validOpponents = 0;

    playerOpponents.forEach(opponentId => {
      const opponentStats = allPlayerStats.get(opponentId);
      if (opponentStats) {
        const opponentMWP = this.calculateMWP(opponentStats.matchPoints, opponentStats.matchesPlayed);
        totalMWP += opponentMWP;
        validOpponents++;
      }
    });

    if (validOpponents === 0) return 0.33; // Minimum 33%

    const omw = totalMWP / validOpponents;
    return Math.max(omw, 0.33); // Minimum 33% per MTG rules
  }

  /**
   * Calculate Opponent Game Win Percentage (OGW%)
   */
  static calculateOGW(
    playerOpponents: string[],
    allPlayerStats: Map<string, PlayerMatchData>
  ): number {
    if (playerOpponents.length === 0) return 0.33; // Minimum 33%

    let totalGWP = 0;
    let validOpponents = 0;

    playerOpponents.forEach(opponentId => {
      const opponentStats = allPlayerStats.get(opponentId);
      if (opponentStats) {
        const opponentGWP = this.calculateGWP(opponentStats.gameWins, opponentStats.gameLosses);
        totalGWP += opponentGWP;
        validOpponents++;
      }
    });

    if (validOpponents === 0) return 0.33; // Minimum 33%

    const ogw = totalGWP / validOpponents;
    return Math.max(ogw, 0.33); // Minimum 33% per MTG rules
  }

  /**
   * Calculate complete standings with tiebreakers
   */
  static calculateStandings(
    players: PlayerWithStats[],
    allPairings: PairingWithPlayers[]
  ): PlayerStanding[] {
    // Calculate basic player stats
    const playerStats = this.calculatePlayerStats(players, allPairings);

    // Calculate tiebreakers for each player
    const standings: PlayerStanding[] = [];

    playerStats.forEach((stats, playerId) => {
      const gwpPercentage = this.calculateGWP(stats.gameWins, stats.gameLosses);
      const omwPercentage = this.calculateOMW(stats.opponents, playerStats);
      const ogwPercentage = this.calculateOGW(stats.opponents, playerStats);

      standings.push({
        ...stats,
        gwpPercentage,
        omwPercentage,
        ogwPercentage,
        position: 0 // Will be set after sorting
      });
    });

    // Sort by tiebreakers (Match Points > OMW% > GWP% > OGW%)
    standings.sort((a, b) => {
      // 1. Match Points (descending)
      if (a.matchPoints !== b.matchPoints) {
        return b.matchPoints - a.matchPoints;
      }

      // 2. OMW% (descending)
      if (Math.abs(a.omwPercentage - b.omwPercentage) > 0.001) {
        return b.omwPercentage - a.omwPercentage;
      }

      // 3. GWP% (descending)
      if (Math.abs(a.gwpPercentage - b.gwpPercentage) > 0.001) {
        return b.gwpPercentage - a.gwpPercentage;
      }

      // 4. OGW% (descending)
      return b.ogwPercentage - a.ogwPercentage;
    });

    // Assign positions
    standings.forEach((standing, index) => {
      standing.position = index + 1;
    });

    return standings;
  }

  /**
   * Update player stats after a single match result
   */
  static updatePlayerStatsFromResult(
    currentStats: TournamentPlayerStats,
    result: MatchResult
  ): Partial<TournamentPlayerStats> {
    const isPlayer1 = currentStats.player_id === result.player1_id;
    const playerId = currentStats.player_id;
    
    if (result.is_bye && isPlayer1) {
      // Player received a bye
      return {
        match_points: currentStats.match_points + 3,
        game_wins: currentStats.game_wins + 2,
        matches_played: currentStats.matches_played + 1,
        byes_received: currentStats.byes_received + 1
      };
    } else if (!result.is_bye && (isPlayer1 || currentStats.player_id === result.player2_id)) {
      // Regular match
      const matchPoints = this.calculateMatchPoints(result.result, playerId, result.player1_id);
      const gameRecord = this.calculateGameRecord(result.result, playerId, result.player1_id);

      return {
        match_points: currentStats.match_points + matchPoints,
        game_wins: currentStats.game_wins + gameRecord.wins,
        game_losses: currentStats.game_losses + gameRecord.losses,
        matches_played: currentStats.matches_played + 1
      };
    }

    return {}; // No changes if player not involved in this match
  }

  /**
   * Format percentage for display
   */
  static formatPercentage(value: number): string {
    return `${(value * 100).toFixed(1)}%`;
  }

  /**
   * Get match record string (W-L-D format)
   */
  static getMatchRecord(matchPoints: number, matchesPlayed: number): string {
    const wins = Math.floor(matchPoints / 3);
    const draws = matchPoints % 3;
    const losses = matchesPlayed - wins - draws;
    
    return `${wins}-${losses}-${draws}`;
  }
}
