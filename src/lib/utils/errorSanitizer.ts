/**
 * Error message sanitization utility
 * 
 * Provides safe error messages for production while preserving
 * detailed information for development environments.
 */

interface SanitizedError {
  message: string;
  code?: string;
  details?: Record<string, unknown>;
}

/**
 * Generic error messages for production use
 */
const GENERIC_MESSAGES = {
  PERMISSION_DENIED: 'Access denied',
  AUTHENTICATION_REQUIRED: 'Authentication required',
  VALIDATION_ERROR: 'Invalid input provided',
  NOT_FOUND: 'Resource not found',
  INTERNAL_ERROR: 'An internal error occurred',
  RATE_LIMITED: 'Too many requests',
  SERVICE_UNAVAILABLE: 'Service temporarily unavailable',
  NETWORK_ERROR: 'Network error occurred',
  DATABASE_ERROR: 'Database operation failed',
  UNKNOWN_ERROR: 'An unexpected error occurred'
} as const;

/**
 * Determines if we're in a production environment
 */
function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

/**
 * Sanitizes error messages for safe client consumption
 * 
 * In development: Returns detailed error information
 * In production: Returns generic, safe error messages
 */
export function sanitizeError(
  error: Error | string | unknown,
  errorType?: keyof typeof GENERIC_MESSAGES,
  context?: Record<string, unknown>
): SanitizedError {
  const isDev = !isProduction();
  
  // Handle string errors
  if (typeof error === 'string') {
    return {
      message: isDev ? error : (errorType ? GENERIC_MESSAGES[errorType] : GENERIC_MESSAGES.UNKNOWN_ERROR),
      code: errorType,
      details: isDev ? { originalMessage: error, context } : undefined
    };
  }
  
  // Handle Error objects
  if (error instanceof Error) {
    // Check if it's a custom error with a type
    const customErrorType = (error as unknown as Record<string, unknown>).type as keyof typeof GENERIC_MESSAGES;
    const finalErrorType = errorType || customErrorType;
    
    return {
      message: isDev ? error.message : (finalErrorType ? GENERIC_MESSAGES[finalErrorType] : GENERIC_MESSAGES.UNKNOWN_ERROR),
      code: finalErrorType,
      details: isDev ? { 
        originalMessage: error.message, 
        stack: error.stack,
        context 
      } : undefined
    };
  }
  
  // Handle unknown error types
  return {
    message: isDev ? String(error) : GENERIC_MESSAGES.UNKNOWN_ERROR,
    code: errorType || 'UNKNOWN_ERROR',
    details: isDev ? { originalError: error, context } : undefined
  };
}

/**
 * Creates a sanitized error response for API routes
 */
export function createErrorResponse(
  error: Error | string | unknown,
  errorType?: keyof typeof GENERIC_MESSAGES,
  context?: Record<string, unknown>
) {
  const sanitized = sanitizeError(error, errorType, context);

  return {
    error: sanitized.message,
    code: sanitized.code,
    ...(sanitized.details && { details: sanitized.details })
  };
}

/**
 * Sanitizes Supabase errors specifically
 */
export function sanitizeSupabaseError(error: unknown): SanitizedError {
  if (!error) {
    return sanitizeError('Unknown database error', 'DATABASE_ERROR');
  }
  
  // Common Supabase error patterns
  const errorObj = error as Record<string, unknown>;
  const errorCode = errorObj.code as string;
  const errorMessage = errorObj.message as string;

  if (errorCode === 'PGRST116') {
    return sanitizeError(errorMessage, 'NOT_FOUND');
  }

  if (errorCode === '42501' || errorMessage?.includes('permission denied')) {
    return sanitizeError(errorMessage, 'PERMISSION_DENIED');
  }

  if (errorCode === '23505' || errorMessage?.includes('duplicate key')) {
    return sanitizeError(errorMessage, 'VALIDATION_ERROR');
  }

  if (errorCode === '23503' || errorMessage?.includes('foreign key')) {
    return sanitizeError(errorMessage, 'VALIDATION_ERROR');
  }

  if (errorCode === '23514' || errorMessage?.includes('check constraint')) {
    return sanitizeError(errorMessage, 'VALIDATION_ERROR');
  }
  
  // Generic database error
  return sanitizeError(errorMessage || error, 'DATABASE_ERROR');
}

/**
 * Sanitizes authentication errors
 */
export function sanitizeAuthError(error: unknown): SanitizedError {
  if (!error) {
    return sanitizeError('Authentication failed', 'AUTHENTICATION_REQUIRED');
  }
  
  // Common auth error patterns
  const errorObj = error as Record<string, unknown>;
  const errorMessage = errorObj.message as string;

  if (errorMessage?.includes('Invalid login credentials')) {
    return sanitizeError(errorMessage, 'AUTHENTICATION_REQUIRED');
  }

  if (errorMessage?.includes('Email not confirmed')) {
    return sanitizeError(errorMessage, 'AUTHENTICATION_REQUIRED');
  }

  if (errorMessage?.includes('Token has expired')) {
    return sanitizeError(errorMessage, 'AUTHENTICATION_REQUIRED');
  }

  if (errorMessage?.includes('Invalid token')) {
    return sanitizeError(errorMessage, 'AUTHENTICATION_REQUIRED');
  }

  return sanitizeError(errorMessage || error, 'AUTHENTICATION_REQUIRED');
}

/**
 * Utility function to log errors safely
 * Logs full details in development, sanitized info in production
 */
export function logError(
  error: Error | string | unknown,
  context?: Record<string, unknown>,
  logger?: { error: (message: string, error?: unknown, context?: unknown) => void }
) {
  const isDev = !isProduction();
  
  if (logger) {
    if (isDev) {
      logger.error('Error occurred', error, context);
    } else {
      const sanitized = sanitizeError(error);
      logger.error(sanitized.message, undefined, { code: sanitized.code, ...context });
    }
  } else {
    // Fallback to console in development
    if (isDev) {
      console.error('Error occurred:', error, context);
    }
  }
}
