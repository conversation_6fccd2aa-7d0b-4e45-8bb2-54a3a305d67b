/**
 * Swiss Pairing Algorithm Implementation
 * 
 * This module implements the Swiss pairing system commonly used in Magic: The Gathering tournaments.
 * 
 * Swiss Pairing Rules:
 * 1. Round 1: Random pairing
 * 2. Subsequent rounds: Pair players with similar match points
 * 3. Avoid repeat pairings when possible
 * 4. Handle byes for odd number of players
 * 5. Bye assignment priority: players who haven't received a bye yet
 */

import {
  PlayerWithStats,
  TournamentPairing,
  PairingWithPlayers,
  TournamentPlayerStats
} from '@/lib/mock/tournamentOrganizerMock';

export interface PairingRequest {
  roundId: string;
  roundNumber: number;
  players: PlayerWithStats[];
  previousPairings: PairingWithPlayers[];
  playerStats: TournamentPlayerStats[];
}

export interface PairingResult {
  pairings: Omit<TournamentPairing, 'id' | 'created_at' | 'updated_at'>[];
  success: boolean;
  error?: string;
}

/**
 * Main Swiss pairing function
 */
export function generateSwissPairings(request: PairingRequest): PairingResult {
  try {
    const { roundId, roundNumber, players, previousPairings, playerStats } = request;

    // Filter out dropped players
    const activePlayers = players.filter(player => {
      const stats = playerStats.find(s => s.player_id === player.id);
      return !stats?.is_dropped;
    });

    if (activePlayers.length === 0) {
      return { pairings: [], success: false, error: 'No active players available for pairing' };
    }

    if (roundNumber === 1) {
      return generateFirstRoundPairings(roundId, activePlayers);
    } else {
      return generateSubsequentRoundPairings(roundId, activePlayers, previousPairings, playerStats);
    }
  } catch (error) {
    return { 
      pairings: [], 
      success: false, 
      error: `Failed to generate pairings: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
}

/**
 * Generate random pairings for the first round
 */
function generateFirstRoundPairings(roundId: string, players: PlayerWithStats[]): PairingResult {
  const shuffledPlayers = [...players].sort(() => Math.random() - 0.5);
  const pairings: Omit<TournamentPairing, 'id' | 'created_at' | 'updated_at'>[] = [];
  
  let tableNumber = 1;
  
  for (let i = 0; i < shuffledPlayers.length; i += 2) {
    if (i + 1 < shuffledPlayers.length) {
      // Regular pairing
      pairings.push({
        round_id: roundId,
        player1_id: shuffledPlayers[i].id,
        player2_id: shuffledPlayers[i + 1].id,
        table_number: tableNumber++,
        is_bye: false
      });
    } else {
      // Bye for the last player (odd number)
      pairings.push({
        round_id: roundId,
        player1_id: shuffledPlayers[i].id,
        is_bye: true
      });
    }
  }

  return { pairings, success: true };
}

/**
 * Generate pairings for subsequent rounds using Swiss system
 */
function generateSubsequentRoundPairings(
  roundId: string,
  players: PlayerWithStats[],
  previousPairings: PairingWithPlayers[],
  playerStats: TournamentPlayerStats[]
): PairingResult {
  // Create player groups by match points
  const playerGroups = groupPlayersByPoints(players, playerStats);
  
  // Get pairing history to avoid repeats
  const pairingHistory = buildPairingHistory(previousPairings);
  
  // Generate pairings
  const pairings: Omit<TournamentPairing, 'id' | 'created_at' | 'updated_at'>[] = [];
  const pairedPlayers = new Set<string>();
  let tableNumber = 1;

  // Process each point group from highest to lowest
  const sortedGroups = Object.keys(playerGroups)
    .map(Number)
    .sort((a, b) => b - a);

  for (const points of sortedGroups) {
    const groupPlayers = playerGroups[points].filter(p => !pairedPlayers.has(p.id));
    
    if (groupPlayers.length === 0) continue;

    // If odd number in group, try to pair down one player to next group
    if (groupPlayers.length % 2 === 1) {
      const playerToPairDown = groupPlayers.pop()!;
      
      // Find next lower group with players
      let pairedDown = false;
      for (let i = sortedGroups.indexOf(points) + 1; i < sortedGroups.length; i++) {
        const lowerPoints = sortedGroups[i];
        const lowerGroupPlayers = playerGroups[lowerPoints].filter(p => !pairedPlayers.has(p.id));
        
        if (lowerGroupPlayers.length > 0) {
          playerGroups[lowerPoints].push(playerToPairDown);
          pairedDown = true;
          break;
        }
      }
      
      // If couldn't pair down, this player gets a bye
      if (!pairedDown) {
        const byePlayer = selectByePlayer([playerToPairDown], playerStats);
        if (byePlayer) {
          pairings.push({
            round_id: roundId,
            player1_id: byePlayer.id,
            is_bye: true
          });
          pairedPlayers.add(byePlayer.id);
        }
      }
    }

    // Pair remaining players in the group
    const remainingPlayers = groupPlayers.filter(p => !pairedPlayers.has(p.id));
    const groupPairings = pairPlayersInGroup(roundId, remainingPlayers, pairingHistory, tableNumber);
    
    pairings.push(...groupPairings.pairings);
    groupPairings.pairedPlayerIds.forEach(id => pairedPlayers.add(id));
    tableNumber = groupPairings.nextTableNumber;
  }

  // Handle any remaining unpaired players with byes
  const unpairedPlayers = players.filter(p => !pairedPlayers.has(p.id));
  if (unpairedPlayers.length > 0) {
    const byePlayer = selectByePlayer(unpairedPlayers, playerStats);
    if (byePlayer) {
      pairings.push({
        round_id: roundId,
        player1_id: byePlayer.id,
        is_bye: true
      });
    }
  }

  return { pairings, success: true };
}

/**
 * Group players by their current match points
 */
function groupPlayersByPoints(players: PlayerWithStats[], playerStats: TournamentPlayerStats[]): Record<number, PlayerWithStats[]> {
  const groups: Record<number, PlayerWithStats[]> = {};
  
  players.forEach(player => {
    const stats = playerStats.find(s => s.player_id === player.id);
    const points = stats?.match_points || 0;
    
    if (!groups[points]) {
      groups[points] = [];
    }
    groups[points].push(player);
  });
  
  return groups;
}

/**
 * Build a history of who has played against whom
 */
function buildPairingHistory(previousPairings: PairingWithPlayers[]): Set<string> {
  const history = new Set<string>();
  
  previousPairings.forEach(pairing => {
    if (!pairing.is_bye && pairing.player2_id) {
      const key1 = `${pairing.player1_id}-${pairing.player2_id}`;
      const key2 = `${pairing.player2_id}-${pairing.player1_id}`;
      history.add(key1);
      history.add(key2);
    }
  });
  
  return history;
}

/**
 * Pair players within a point group, avoiding repeat pairings
 */
function pairPlayersInGroup(
  roundId: string,
  players: PlayerWithStats[],
  pairingHistory: Set<string>,
  startingTableNumber: number
): { pairings: Omit<TournamentPairing, 'id' | 'created_at' | 'updated_at'>[]; pairedPlayerIds: string[]; nextTableNumber: number } {
  const pairings: Omit<TournamentPairing, 'id' | 'created_at' | 'updated_at'>[] = [];
  const pairedPlayerIds: string[] = [];
  let tableNumber = startingTableNumber;
  
  const availablePlayers = [...players];
  
  while (availablePlayers.length >= 2) {
    const player1 = availablePlayers.shift()!;
    let player2: PlayerWithStats | null = null;
    
    // Find a player who hasn't played against player1
    for (let i = 0; i < availablePlayers.length; i++) {
      const candidate = availablePlayers[i];
      const pairingKey = `${player1.id}-${candidate.id}`;
      
      if (!pairingHistory.has(pairingKey)) {
        player2 = candidate;
        availablePlayers.splice(i, 1);
        break;
      }
    }
    
    // If no valid opponent found, pair with the first available player
    if (!player2 && availablePlayers.length > 0) {
      player2 = availablePlayers.shift()!;
    }
    
    if (player2) {
      pairings.push({
        round_id: roundId,
        player1_id: player1.id,
        player2_id: player2.id,
        table_number: tableNumber++,
        is_bye: false
      });
      
      pairedPlayerIds.push(player1.id, player2.id);
    }
  }
  
  return { pairings, pairedPlayerIds, nextTableNumber: tableNumber };
}

/**
 * Select a player to receive a bye (prioritize players who haven't had one)
 */
function selectByePlayer(players: PlayerWithStats[], playerStats: TournamentPlayerStats[]): PlayerWithStats | null {
  if (players.length === 0) return null;
  
  // First, try to find a player who hasn't received a bye yet
  const playersWithoutBye = players.filter(player => {
    const stats = playerStats.find(s => s.player_id === player.id);
    return (stats?.byes_received || 0) === 0;
  });
  
  if (playersWithoutBye.length > 0) {
    // Among players without byes, select the one with the lowest match points
    return playersWithoutBye.reduce((lowest, current) => {
      const lowestStats = playerStats.find(s => s.player_id === lowest.id);
      const currentStats = playerStats.find(s => s.player_id === current.id);
      
      const lowestPoints = lowestStats?.match_points || 0;
      const currentPoints = currentStats?.match_points || 0;
      
      return currentPoints < lowestPoints ? current : lowest;
    });
  }
  
  // If all players have had byes, select the one with the fewest byes and lowest points
  return players.reduce((best, current) => {
    const bestStats = playerStats.find(s => s.player_id === best.id);
    const currentStats = playerStats.find(s => s.player_id === current.id);
    
    const bestByes = bestStats?.byes_received || 0;
    const currentByes = currentStats?.byes_received || 0;
    
    if (currentByes < bestByes) return current;
    if (currentByes > bestByes) return best;
    
    // Same number of byes, compare match points
    const bestPoints = bestStats?.match_points || 0;
    const currentPoints = currentStats?.match_points || 0;
    
    return currentPoints < bestPoints ? current : best;
  });
}
