/**
 * Mock data for Tournament Organizer feature
 * This file contains comprehensive mock data for testing the tournament organizer
 * functionality before database integration.
 */

import type { Database } from '@/lib/supabase/types';

// Type aliases for convenience
type Player = Database['public']['Tables']['players']['Row'];
type Tournament = Database['public']['Tables']['tournaments']['Row'];
type TournamentRegistration = Database['public']['Tables']['tournament_registrations']['Row'];

// Tournament Organizer specific types (will match future database schema)
export interface TournamentOrganizerSettings {
  id: string;
  tournament_id: string;
  rounds_total: number;
  round_duration_minutes: number;
  current_round: number;
  status: 'setup' | 'active' | 'paused' | 'completed';
  created_at: string;
  updated_at: string;
}

export interface TournamentRound {
  id: string;
  tournament_id: string;
  round_number: number;
  status: 'pending' | 'active' | 'completed';
  started_at?: string;
  ended_at?: string;
  duration_minutes?: number;
  created_at: string;
}

export interface TournamentPairing {
  id: string;
  round_id: string;
  player1_id: string;
  player2_id?: string;
  table_number?: number;
  is_bye: boolean;
  result?: '2-0' | '2-1' | '1-2' | '0-2' | '1-0' | '0-1' | '0-0';
  winner_id?: string;
  created_at: string;
  updated_at: string;
}

export interface TournamentPlayerStats {
  id: string;
  tournament_id: string;
  player_id: string;
  match_points: number;
  game_wins: number;
  game_losses: number;
  matches_played: number;
  byes_received: number;
  is_dropped: boolean;
  omw_percentage: number;
  gwp_percentage: number;
  created_at: string;
  updated_at: string;
}

// Extended types for UI
export interface PlayerWithStats extends Player {
  stats?: TournamentPlayerStats;
  points: number;
  wins: number;
  losses: number;
}

export interface PairingWithPlayers extends TournamentPairing {
  player1: PlayerWithStats;
  player2?: PlayerWithStats;
}

// Mock Players Data
export const mockPlayers: PlayerWithStats[] = [
  {
    id: 'player-1',
    first_name: 'Marco',
    last_name: 'Rossi',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    points: 0,
    wins: 0,
    losses: 0
  },
  {
    id: 'player-2',
    first_name: 'Luca',
    last_name: 'Bianchi',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    points: 0,
    wins: 0,
    losses: 0
  },
  {
    id: 'player-3',
    first_name: 'Anna',
    last_name: 'Verdi',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    points: 0,
    wins: 0,
    losses: 0
  },
  {
    id: 'player-4',
    first_name: 'Sofia',
    last_name: 'Neri',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    points: 0,
    wins: 0,
    losses: 0
  },
  {
    id: 'player-5',
    first_name: 'Giuseppe',
    last_name: 'Gialli',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    points: 0,
    wins: 0,
    losses: 0
  },
  {
    id: 'player-6',
    first_name: 'Elena',
    last_name: 'Blu',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    points: 0,
    wins: 0,
    losses: 0
  },
  {
    id: 'player-7',
    first_name: 'Francesco',
    last_name: 'Viola',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    points: 0,
    wins: 0,
    losses: 0
  },
  {
    id: 'player-8',
    first_name: 'Chiara',
    last_name: 'Rosa',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    points: 0,
    wins: 0,
    losses: 0
  }
];

// Mock Tournament Organizer Settings
export const mockTournamentOrganizerSettings: TournamentOrganizerSettings = {
  id: 'organizer-settings-1',
  tournament_id: 'tournament-1',
  rounds_total: 4,
  round_duration_minutes: 50,
  current_round: 1,
  status: 'setup',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

// Mock Tournament Rounds
export const mockTournamentRounds: TournamentRound[] = [
  {
    id: 'round-1',
    tournament_id: 'tournament-1',
    round_number: 1,
    status: 'pending',
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'round-2',
    tournament_id: 'tournament-1',
    round_number: 2,
    status: 'pending',
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'round-3',
    tournament_id: 'tournament-1',
    round_number: 3,
    status: 'pending',
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'round-4',
    tournament_id: 'tournament-1',
    round_number: 4,
    status: 'pending',
    created_at: '2024-01-01T00:00:00Z'
  }
];

// Mock Tournament Player Stats
export const mockTournamentPlayerStats: TournamentPlayerStats[] = mockPlayers.map(player => ({
  id: `stats-${player.id}`,
  tournament_id: 'tournament-1',
  player_id: player.id,
  match_points: 0,
  game_wins: 0,
  game_losses: 0,
  matches_played: 0,
  byes_received: 0,
  is_dropped: false,
  omw_percentage: 0,
  gwp_percentage: 0,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}));

// Mock Tournament Pairings (empty initially)
export const mockTournamentPairings: PairingWithPlayers[] = [];

// Helper function to get player display name
export const getPlayerDisplayName = (player: PlayerWithStats): string => {
  return `${player.first_name} ${player.last_name}`;
};

// Helper function to generate unique IDs
export const generateId = (prefix: string = 'id'): string => {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// Mock data state (will be replaced by database in Phase 4)
export const mockDataState = {
  players: [...mockPlayers],
  organizerSettings: { ...mockTournamentOrganizerSettings },
  rounds: [...mockTournamentRounds],
  pairings: [...mockTournamentPairings],
  playerStats: [...mockTournamentPlayerStats]
};
