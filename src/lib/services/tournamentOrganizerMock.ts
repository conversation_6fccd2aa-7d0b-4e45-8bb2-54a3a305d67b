/**
 * Mock Tournament Organizer Service
 * Provides the same interface as the future database service but uses local state
 * This allows UI development and testing without database dependency
 */

import {
  TournamentOrganizerSettings,
  TournamentRound,
  TournamentPairing,
  TournamentPlayerStats,
  PlayerWithStats,
  PairingWithPlayers,
  mockDataState,
  generateId,
  getPlayerDisplayName
} from '@/lib/mock/tournamentOrganizerMock';

// Service response types
export interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
}

export interface ServiceListResponse<T> {
  data: T[];
  error: string | null;
}

/**
 * Tournament Organizer Settings Service
 */
export class TournamentOrganizerSettingsService {
  static async create(tournamentId: string, settings: Partial<TournamentOrganizerSettings>): Promise<ServiceResponse<TournamentOrganizerSettings>> {
    try {
      const newSettings: TournamentOrganizerSettings = {
        id: generateId('organizer'),
        tournament_id: tournamentId,
        rounds_total: settings.rounds_total || 4,
        round_duration_minutes: settings.round_duration_minutes || 50,
        current_round: 0,
        status: 'setup',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        ...settings
      };

      mockDataState.organizerSettings = newSettings;
      
      return { data: newSettings, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to create tournament organizer settings' };
    }
  }

  static async getByTournamentId(tournamentId: string): Promise<ServiceResponse<TournamentOrganizerSettings>> {
    try {
      if (mockDataState.organizerSettings.tournament_id === tournamentId) {
        return { data: mockDataState.organizerSettings, error: null };
      }
      return { data: null, error: 'Tournament organizer settings not found' };
    } catch (error) {
      return { data: null, error: 'Failed to fetch tournament organizer settings' };
    }
  }

  static async update(id: string, updates: Partial<TournamentOrganizerSettings>): Promise<ServiceResponse<TournamentOrganizerSettings>> {
    try {
      if (mockDataState.organizerSettings.id === id) {
        mockDataState.organizerSettings = {
          ...mockDataState.organizerSettings,
          ...updates,
          updated_at: new Date().toISOString()
        };
        return { data: mockDataState.organizerSettings, error: null };
      }
      return { data: null, error: 'Tournament organizer settings not found' };
    } catch (error) {
      return { data: null, error: 'Failed to update tournament organizer settings' };
    }
  }
}

/**
 * Tournament Rounds Service
 */
export class TournamentRoundsService {
  static async getByTournamentId(tournamentId: string): Promise<ServiceListResponse<TournamentRound>> {
    try {
      const rounds = mockDataState.rounds.filter(round => round.tournament_id === tournamentId);
      return { data: rounds, error: null };
    } catch (error) {
      return { data: [], error: 'Failed to fetch tournament rounds' };
    }
  }

  static async create(tournamentId: string, roundNumber: number): Promise<ServiceResponse<TournamentRound>> {
    try {
      const newRound: TournamentRound = {
        id: generateId('round'),
        tournament_id: tournamentId,
        round_number: roundNumber,
        status: 'pending',
        created_at: new Date().toISOString()
      };

      mockDataState.rounds.push(newRound);
      return { data: newRound, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to create tournament round' };
    }
  }

  static async update(id: string, updates: Partial<TournamentRound>): Promise<ServiceResponse<TournamentRound>> {
    try {
      const roundIndex = mockDataState.rounds.findIndex(round => round.id === id);
      if (roundIndex !== -1) {
        mockDataState.rounds[roundIndex] = {
          ...mockDataState.rounds[roundIndex],
          ...updates
        };
        return { data: mockDataState.rounds[roundIndex], error: null };
      }
      return { data: null, error: 'Tournament round not found' };
    } catch (error) {
      return { data: null, error: 'Failed to update tournament round' };
    }
  }
}

/**
 * Tournament Pairings Service
 */
export class TournamentPairingsService {
  static async getByRoundId(roundId: string): Promise<ServiceListResponse<PairingWithPlayers>> {
    try {
      const pairings = mockDataState.pairings.filter(pairing => pairing.round_id === roundId);
      return { data: pairings, error: null };
    } catch (error) {
      return { data: [], error: 'Failed to fetch tournament pairings' };
    }
  }

  static async createBatch(pairings: Omit<TournamentPairing, 'id' | 'created_at' | 'updated_at'>[]): Promise<ServiceListResponse<PairingWithPlayers>> {
    try {
      const newPairings: PairingWithPlayers[] = pairings.map(pairing => {
        const player1 = mockDataState.players.find(p => p.id === pairing.player1_id);
        const player2 = pairing.player2_id ? mockDataState.players.find(p => p.id === pairing.player2_id) : undefined;

        if (!player1) {
          throw new Error(`Player not found: ${pairing.player1_id}`);
        }

        return {
          id: generateId('pairing'),
          round_id: pairing.round_id,
          player1_id: pairing.player1_id,
          player2_id: pairing.player2_id,
          table_number: pairing.table_number,
          is_bye: pairing.is_bye,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          player1,
          player2
        };
      });

      mockDataState.pairings.push(...newPairings);
      return { data: newPairings, error: null };
    } catch (error) {
      return { data: [], error: `Failed to create tournament pairings: ${error}` };
    }
  }

  static async updateResult(id: string, result: string, winnerId?: string): Promise<ServiceResponse<PairingWithPlayers>> {
    try {
      const pairingIndex = mockDataState.pairings.findIndex(pairing => pairing.id === id);
      if (pairingIndex !== -1) {
        mockDataState.pairings[pairingIndex] = {
          ...mockDataState.pairings[pairingIndex],
          result: result as any,
          winner_id: winnerId,
          updated_at: new Date().toISOString()
        };
        return { data: mockDataState.pairings[pairingIndex], error: null };
      }
      return { data: null, error: 'Tournament pairing not found' };
    } catch (error) {
      return { data: null, error: 'Failed to update tournament pairing result' };
    }
  }

  static async deleteByRoundId(roundId: string): Promise<ServiceResponse<boolean>> {
    try {
      mockDataState.pairings = mockDataState.pairings.filter(pairing => pairing.round_id !== roundId);
      return { data: true, error: null };
    } catch (error) {
      return { data: false, error: 'Failed to delete tournament pairings' };
    }
  }
}

/**
 * Tournament Player Stats Service
 */
export class TournamentPlayerStatsService {
  static async getByTournamentId(tournamentId: string): Promise<ServiceListResponse<TournamentPlayerStats>> {
    try {
      const stats = mockDataState.playerStats.filter(stat => stat.tournament_id === tournamentId);
      return { data: stats, error: null };
    } catch (error) {
      return { data: [], error: 'Failed to fetch tournament player stats' };
    }
  }

  static async updatePlayerStats(playerId: string, tournamentId: string, updates: Partial<TournamentPlayerStats>): Promise<ServiceResponse<TournamentPlayerStats>> {
    try {
      const statIndex = mockDataState.playerStats.findIndex(
        stat => stat.player_id === playerId && stat.tournament_id === tournamentId
      );
      
      if (statIndex !== -1) {
        mockDataState.playerStats[statIndex] = {
          ...mockDataState.playerStats[statIndex],
          ...updates,
          updated_at: new Date().toISOString()
        };
        return { data: mockDataState.playerStats[statIndex], error: null };
      }
      return { data: null, error: 'Player stats not found' };
    } catch (error) {
      return { data: null, error: 'Failed to update player stats' };
    }
  }

  static async calculateStandings(tournamentId: string): Promise<ServiceListResponse<PlayerWithStats>> {
    try {
      const stats = mockDataState.playerStats.filter(stat => stat.tournament_id === tournamentId);
      const playersWithStats: PlayerWithStats[] = stats.map(stat => {
        const player = mockDataState.players.find(p => p.id === stat.player_id);
        if (!player) throw new Error(`Player not found: ${stat.player_id}`);
        
        return {
          ...player,
          stats: stat,
          points: stat.match_points,
          wins: stat.game_wins,
          losses: stat.game_losses
        };
      });

      // Sort by match points, then OMW%, then GWP%
      playersWithStats.sort((a, b) => {
        if (a.stats!.match_points !== b.stats!.match_points) {
          return b.stats!.match_points - a.stats!.match_points;
        }
        if (a.stats!.omw_percentage !== b.stats!.omw_percentage) {
          return b.stats!.omw_percentage - a.stats!.omw_percentage;
        }
        return b.stats!.gwp_percentage - a.stats!.gwp_percentage;
      });

      return { data: playersWithStats, error: null };
    } catch (error) {
      return { data: [], error: 'Failed to calculate standings' };
    }
  }
}

/**
 * Combined Tournament Organizer Service
 * Main service that orchestrates all tournament organizer operations
 */
export class TournamentOrganizerService {
  static settings = TournamentOrganizerSettingsService;
  static rounds = TournamentRoundsService;
  static pairings = TournamentPairingsService;
  static playerStats = TournamentPlayerStatsService;

  static async getPlayers(tournamentId: string): Promise<ServiceListResponse<PlayerWithStats>> {
    try {
      // In real implementation, this would fetch registered players from tournament_registrations
      // For now, return all mock players
      return { data: mockDataState.players, error: null };
    } catch (error) {
      return { data: [], error: 'Failed to fetch tournament players' };
    }
  }

  static async resetMockData(): Promise<void> {
    // Reset all mock data to initial state
    const { mockPlayers, mockTournamentOrganizerSettings, mockTournamentRounds, mockTournamentPlayerStats } = await import('@/lib/mock/tournamentOrganizerMock');
    
    mockDataState.players = [...mockPlayers];
    mockDataState.organizerSettings = { ...mockTournamentOrganizerSettings };
    mockDataState.rounds = [...mockTournamentRounds];
    mockDataState.pairings = [];
    mockDataState.playerStats = [...mockTournamentPlayerStats];
  }
}
