/**
 * Navigation configuration constants
 */

export interface NavLink {
  href: string;
  label: string;
  id: string;
  isExternal?: boolean;
}

export interface ToolLink {
  href: string;
  label: string;
  id: string;
}

// Main navigation links
export const NAV_LINKS: NavLink[] = [
  { 
    href: "/", 
    label: "Home", 
    id: "home" 
  },
  { 
    href: "/calendar", 
    label: "Calendario", 
    id: "calendar" 
  },
  { 
    href: "/classifica", 
    label: "Classifica", 
    id: "ranking" 
  },
  { 
    href: "/regolamento", 
    label: "Regolamento", 
    id: "regolamento" 
  },
  { 
    href: "https://magic.wizards.com/formats/pauper", 
    label: "Cos'è Pauper?", 
    id: "pauper",
    isExternal: true 
  }
];

// Tools dropdown links
export const TOOLS_LINKS: ToolLink[] = [
  { 
    href: "/tools/life-counter", 
    label: "Life Counter", 
    id: "life-counter" 
  },
  { 
    href: "/tools/mana-counter", 
    label: "Mana Counter", 
    id: "mana-counter" 
  }
];

// Index after which to insert the Tools dropdown (after "Classifica")
export const TOOLS_INSERT_INDEX = NAV_LINKS.findIndex(link => link.id === "ranking");

export type CurrentPage = "home" | "calendar" | "ranking" | "regolamento" | "tools" | "admin";
