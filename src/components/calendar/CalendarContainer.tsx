import { Tournament, ViewMode, TournamentFilters } from "@/types/calendar";
import { CalendarHeader } from "./CalendarHeader";
import { CalendarGrid } from "./CalendarGrid";
import { CalendarLegend } from "./CalendarLegend";
import { TournamentDetails } from "./TournamentDetails";
import { TournamentCardsView } from "./TournamentCardsView";
import { Spinner } from "../ui/Spinner";

interface CalendarContainerProps {
  currentMonth: number;
  currentYear: number;
  firstDayOfMonth: number;
  daysInMonth: number;
  tournamentDays: Record<number, Tournament[]>;
  selectedDate: Date | null;
  selectedTournament: Tournament | null;
  isLoading?: boolean;
  onPrevMonth: () => void;
  onNextMonth: () => void;
  onDayClick: (day: number) => void;
  onTournamentClick: (tournament: Tournament) => void;
  onRegistrationClick: () => void;
  onEditClick?: (tournament: Tournament) => void;
  onCreateEvent?: (date: Date) => void;
  onTournamentUpdate?: (tournament: Tournament) => void;
  // View mode props
  viewMode: ViewMode;
  // Filters
  filters?: TournamentFilters;
}

export function CalendarContainer({
  currentMonth,
  currentYear,
  firstDayOfMonth,
  daysInMonth,
  tournamentDays,
  selectedDate,
  selectedTournament,
  isLoading,
  onPrevMonth,
  onNextMonth,
  onDayClick,
  onTournamentClick,
  onRegistrationClick,
  onEditClick,
  onCreateEvent,
  onTournamentUpdate,
  viewMode,
  filters,
}: CalendarContainerProps) {
  // Render cards view if selected
  if (viewMode === 'cards') {
    return (
      <div className="relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/20 backdrop-blur-sm z-10 rounded-xl">
            <Spinner />
          </div>
        )}
        
        <TournamentCardsView
          selectedTournament={selectedTournament}
          onTournamentClick={onTournamentClick}
          onRegistrationClick={onRegistrationClick}
          onEditClick={onEditClick}
          filters={filters}
        />
      </div>
    );
  }

  // Default calendar view
  return (
    <div className="flex flex-col lg:flex-row gap-4 sm:gap-6">
      {/* Calendar */}
      <div className="lg:w-3/4 bg-black/20 backdrop-blur-sm rounded-xl border border-blue-500/30 overflow-hidden">
        <CalendarHeader
          currentMonth={currentMonth}
          currentYear={currentYear}
          onPrevMonth={onPrevMonth}
          onNextMonth={onNextMonth}
        />
        
        <div className="relative p-4">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/20 backdrop-blur-sm z-10">
              <Spinner />
            </div>
          )}
          
          <CalendarGrid
            currentMonth={currentMonth}
            currentYear={currentYear}
            firstDayOfMonth={firstDayOfMonth}
            daysInMonth={daysInMonth}
            tournamentDays={tournamentDays}
            selectedDate={selectedDate}
            selectedTournament={selectedTournament}
            onDayClick={onDayClick}
            onTournamentClick={onTournamentClick}
            onRegistrationClick={onRegistrationClick}
            onCreateEvent={onCreateEvent}
          />
        </div>
        
        <CalendarLegend />
      </div>
      
      {/* Tournament Details */}
      <div className="lg:w-1/4">
        <TournamentDetails
          selectedTournament={selectedTournament}
          selectedDate={selectedDate}
          onRegistrationClick={onRegistrationClick}
          onEditClick={onEditClick}
          onTournamentUpdate={onTournamentUpdate}
        />
      </div>
    </div>
  );
} 