"use client";

import React, { useState, useEffect } from "react";
import { X, Check, AlertCircle, CalendarIcon, Clock, MapPin, Coins, Gamepad2, UserPlus } from "lucide-react";
import { Tournament } from "@/types/calendar";
import { Colors } from "@/lib/constants/colors";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { useArchetypes } from "@/lib/hooks/useArchetypes";
import { useLockBodyScroll } from "@/lib/hooks/useLockBodyScroll";
import { useAdminCreatePlayerRegistration } from "@/lib/hooks/useRegistration";
import { Select } from "@/components/ui/Select";
import { validateManaboxUrl, formatManaboxUrl } from "@/lib/utils/manaboxValidation";

interface AdminAddPlayerModalProps {
  tournament: Tournament | null;
  isOpen: boolean;
  onClose: () => void;
}

interface AdminFormData {
  fullName: string;
  email: string;
  deckName: string;
  archetype: string;
  decklist: string;
}

interface AdminFormErrors {
  fullName: boolean;
  email: boolean;
  deckName: boolean;
  archetype: boolean;
  decklist: boolean;
}

export function AdminAddPlayerModal({ tournament, isOpen, onClose }: AdminAddPlayerModalProps) {
  const { data: archetypes, isLoading: isLoadingArchetypes } = useArchetypes();
  const { mutate: createPlayerRegistration, isPending: isCreating, isSuccess, error: registrationError } = useAdminCreatePlayerRegistration();
  
  // Lock body scroll when modal is open
  useLockBodyScroll(isOpen);
  
  const [formData, setFormData] = useState<AdminFormData>({
    fullName: "",
    email: "",
    deckName: "",
    archetype: "",
    decklist: ""
  });
  
  const [errors, setErrors] = useState<AdminFormErrors>({
    fullName: false,
    email: false,
    deckName: false,
    archetype: false,
    decklist: false
  });
  
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        fullName: "",
        email: "",
        deckName: "",
        archetype: "",
        decklist: ""
      });
      setErrors({
        fullName: false,
        email: false,
        deckName: false,
        archetype: false,
        decklist: false
      });
      setValidationErrors({});
      setIsSubmitted(false);
    }
  }, [isOpen]);

  // Handle success
  useEffect(() => {
    if (isSuccess) {
      setIsSubmitted(true);
    }
  }, [isSuccess]);

  if (!isOpen || !tournament) return null;

  const storeColor = Colors.getStoreScheme(tournament.store, tournament);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear errors when user starts typing
    if (errors[name as keyof AdminFormErrors]) {
      setErrors(prev => ({ ...prev, [name]: false }));
    }
    if (validationErrors[name]) {
      setValidationErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  const handleArchetypeChange = (value: string) => {
    setFormData(prev => ({ ...prev, archetype: value }));
    if (errors.archetype) {
      setErrors(prev => ({ ...prev, archetype: false }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Reset validation errors
    setErrors({
      fullName: false,
      email: false,
      deckName: false,
      archetype: false,
      decklist: false
    });
    setValidationErrors({});

    // Validate required fields
    const newErrors = {
      fullName: false,
      email: false,
      deckName: false,
      archetype: false,
      decklist: false
    };

    // Email is now required
    if (!formData.email.trim()) {
      newErrors.email = true;
    }

    // Archetype is now required
    if (!formData.archetype.trim()) {
      newErrors.archetype = true;
    }

    // Check if there are any validation errors
    if (Object.values(newErrors).some(error => error)) {
      setErrors(newErrors);
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email.trim())) {
      setValidationErrors({
        email: "Inserisci un indirizzo email valido"
      });
      return;
    }

    // Validate decklist URL format if provided
    if (formData.decklist.trim() && !validateManaboxUrl(formData.decklist.trim())) {
      setValidationErrors({
        decklist: "URL non valido. Inserisci un link valido di Manabox (es. https://manabox.app/decks/...)"
      });
      return;
    }

    // Preparazione dati per API
    const formattedDecklistUrl = formData.decklist.trim() ? formatManaboxUrl(formData.decklist.trim()) : null;

    // Invio dati al server
    if (tournament) {
      createPlayerRegistration({
        tournamentId: tournament.id,
        fullName: formData.fullName.trim(),
        email: formData.email.trim(),
        deckName: formData.deckName.trim() || undefined,
        archetypeId: formData.archetype.trim(),
        deckListUrl: formattedDecklistUrl || undefined
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 rounded-xl border border-blue-500/30 w-full max-w-2xl max-h-[85vh] sm:max-h-[98vh] overflow-hidden">
        {/* Header del Modale */}
        <div className="p-3 sm:p-4 border-b border-blue-500/20 flex justify-between items-center">
          <h3 className="text-lg sm:text-xl font-bold">
            {isSubmitted ? "Giocatore Aggiunto" : `Aggiungi Giocatore - ${tournament.title}`}
          </h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-blue-900/50 rounded-full transition-colors"
          >
            <X size={18} className="sm:w-5 sm:h-5" />
          </button>
        </div>

        {/* Contenuto del Modale */}
        <div className="p-6 overflow-y-auto max-h-[calc(85vh-120px)] sm:max-h-[calc(98vh-120px)]">
          {isSubmitted ? (
            <div className="flex flex-col items-center justify-center py-8">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mb-4">
                <Check size={32} className="text-green-400" />
              </div>
              <h4 className="text-xl font-bold mb-2">Giocatore Aggiunto!</h4>
              <p className="text-center text-blue-200 mb-6">
                Il giocatore è stato registrato con successo per {tournament.title}.
              </p>
              <button
                onClick={onClose}
                className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 ${storeColor.bg} hover:${storeColor.bgHover} shadow-lg hover:shadow-xl`}
              >
                Chiudi
              </button>
            </div>
          ) : registrationError ? (
            <div className="flex flex-col items-center justify-center py-8">
              <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-4">
                <AlertCircle size={32} className="text-red-400" />
              </div>
              <h4 className="text-xl font-bold mb-2">Errore</h4>
              <p className="text-center text-blue-200">
                {registrationError instanceof Error 
                  ? registrationError.message 
                  : "Si è verificato un errore durante la registrazione. Riprova più tardi."}
              </p>
              <button 
                onClick={() => {
                  // Reset dell'errore e permetti di riprovare
                  setIsSubmitted(false);
                }}
                className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm transition-colors"
              >
                Riprova
              </button>
            </div>
          ) : (
            <>
              {/* Riepilogo Torneo */}
              <div className={`mb-4 p-3 rounded-lg bg-black/30 border ${storeColor.border}`}>
                <div className="flex flex-col space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 flex items-center justify-center">
                      <CalendarIcon size={16} className={storeColor.light} />
                    </div>
                    <span className="text-sm">
                      {format(new Date(tournament.date), "EEEE d MMMM", { locale: it })}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 flex items-center justify-center">
                      <Gamepad2 size={16} className={storeColor.light} />
                    </div>
                    <span className="text-sm capitalize">Formato: {tournament.format}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 flex items-center justify-center">
                      <Clock size={16} className={storeColor.light} />
                    </div>
                    <span className="text-sm">
                      {format(new Date(`2000-01-01T${tournament.time_start}`), "HH:mm")}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 flex items-center justify-center">
                      <MapPin size={16} className={storeColor.light} />
                    </div>
                    <span className="text-sm">{tournament.store?.name || 'Sede da definire'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 flex items-center justify-center">
                      <Coins size={16} className={storeColor.light} />
                    </div>
                    <span className="text-sm">Prezzo: {tournament.price} €</span>
                  </div>
                </div>
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="fullName" className="flex items-center gap-2 text-sm font-medium mb-1">
                    <UserPlus size={16} className={storeColor.light} />
                    Nome e Cognome
                    <span className="text-xs text-blue-300/70 bg-blue-500/10 px-2 py-0.5 rounded">
                      opzionale
                    </span>
                  </label>
                  <input
                    type="text"
                    id="fullName"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 bg-black/30 border ${
                      errors.fullName ? "border-red-500" : "border-blue-500/30"
                    } rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50`}
                    placeholder="Inserisci nome e cognome del giocatore"
                  />
                  {errors.fullName && (
                    <p className="mt-1 text-xs text-red-400">Campo obbligatorio</p>
                  )}
                </div>

                <div>
                  <label htmlFor="email" className="flex items-center gap-2 text-sm font-medium mb-1">
                    Indirizzo Email
                    <span className="text-xs text-red-300/70 bg-red-500/10 px-2 py-0.5 rounded">
                      obbligatorio
                    </span>
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 bg-black/30 border ${
                      errors.email || validationErrors.email ? "border-red-500" : "border-blue-500/30"
                    } rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50`}
                    placeholder="Inserisci l'indirizzo email del giocatore"
                    required
                  />
                  {(errors.email || validationErrors.email) && (
                    <p className="mt-1 text-xs text-red-400">
                      {validationErrors.email || "Campo obbligatorio"}
                    </p>
                  )}
                </div>

                <div>
                  <label htmlFor="deckName" className="flex items-center gap-2 text-sm font-medium mb-1">
                    Nome Deck
                    <span className="text-xs text-blue-300/70 bg-blue-500/10 px-2 py-0.5 rounded">
                      opzionale
                    </span>
                  </label>
                  <input
                    type="text"
                    id="deckName"
                    name="deckName"
                    value={formData.deckName}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 bg-black/30 border ${
                      errors.deckName ? "border-red-500" : "border-blue-500/30"
                    } rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50`}
                    placeholder="Es. Mono Red Burn, Boros Bully, ecc."
                  />
                  {errors.deckName && (
                    <p className="mt-1 text-xs text-red-400">Campo obbligatorio</p>
                  )}
                </div>

                <div>
                  <label htmlFor="archetype" className="flex items-center gap-2 text-sm font-medium mb-1">
                    Archetipo
                    <span className="text-xs text-red-300/70 bg-red-500/10 px-2 py-0.5 rounded">
                      obbligatorio
                    </span>
                  </label>
                  <Select
                    value={formData.archetype}
                    onChange={handleArchetypeChange}
                    options={archetypes?.map(archetype => ({
                      value: archetype.id,
                      label: archetype.name,
                      description: archetype.description || undefined
                    })) || []}
                    placeholder={isLoadingArchetypes ? "Caricamento..." : "Seleziona l'archetipo del deck"}
                    hasError={errors.archetype}
                    disabled={isLoadingArchetypes}
                    allowEmpty={false}
                    emptyLabel="Seleziona un archetipo"
                  />
                  {errors.archetype && (
                    <p className="mt-1 text-xs text-red-400">Campo obbligatorio</p>
                  )}
                </div>

                <div>
                  <label htmlFor="decklist" className="flex items-center gap-2 text-sm font-medium mb-1">
                    Link decklist Manabox
                    <span className="text-xs text-blue-300/70 bg-blue-500/10 px-2 py-0.5 rounded">
                      opzionale
                    </span>
                  </label>
                  <input
                    type="text"
                    id="decklist"
                    name="decklist"
                    value={formData.decklist}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 bg-black/30 border ${
                      errors.decklist || validationErrors.decklist ? "border-red-500" : "border-blue-500/30"
                    } rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50`}
                    placeholder="https://manabox.app/decks/..."
                  />
                  {(errors.decklist || validationErrors.decklist) && (
                    <p className="mt-1 text-xs text-red-400">
                      {validationErrors.decklist || "Campo obbligatorio"}
                    </p>
                  )}
                  <p className="mt-1 text-xs text-blue-300/70">
                    Inserisci il link della decklist su Manabox per condividerla con gli altri giocatori
                  </p>
                </div>

                {/* Submit Button */}
                <div className="pt-4">
                  <button
                    type="submit"
                    disabled={isCreating}
                    className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
                      isCreating
                        ? "bg-gray-600 cursor-not-allowed"
                        : `${storeColor.bg} hover:${storeColor.bgHover} shadow-lg hover:shadow-xl`
                    }`}
                  >
                    {isCreating ? (
                      <div className="flex items-center justify-center gap-2">
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        Registrazione in corso...
                      </div>
                    ) : (
                      <div className="flex items-center justify-center gap-2">
                        <UserPlus size={18} />
                        Aggiungi Giocatore
                      </div>
                    )}
                  </button>
                </div>
              </form>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
