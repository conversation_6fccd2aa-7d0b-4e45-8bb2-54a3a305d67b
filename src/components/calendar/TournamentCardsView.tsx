"use client";

import React, { useState, useMemo } from 'react';
import { Tournament, TournamentFilters } from '@/types/calendar';
import { TournamentCard } from './TournamentCard';
import { TournamentResultsModal } from './TournamentDetails';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { useFilteredTournaments } from '@/hooks/useFilteredTournaments';

interface TournamentCardsViewProps {
  selectedTournament: Tournament | null;
  onTournamentClick: (tournament: Tournament) => void;
  onRegistrationClick: () => void;
  onEditClick?: (tournament: Tournament) => void;
  filters?: TournamentFilters;
}

export function TournamentCardsView({
  selectedTournament,
  onTournamentClick,
  onRegistrationClick,
  onEditClick,
  filters
}: TournamentCardsViewProps) {
  const {
    tournaments: allFilteredTournaments,
    isLoading
  } = useFilteredTournaments(filters);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(0);
  const TOURNAMENTS_PER_PAGE = 12;

  // Results modal state
  const [showResultsModal, setShowResultsModal] = useState(false);
  const [selectedTournamentForResults, setSelectedTournamentForResults] = useState<Tournament | null>(null);
  
  // Paginate the filtered tournaments
  const { tournaments, hasMore } = useMemo(() => {
    const startIndex = 0;
    const endIndex = (currentPage + 1) * TOURNAMENTS_PER_PAGE;
    const paginatedTournaments = allFilteredTournaments.slice(startIndex, endIndex);
    const hasMore = endIndex < allFilteredTournaments.length;
    
    return {
      tournaments: paginatedTournaments,
      hasMore
    };
  }, [allFilteredTournaments, currentPage]);
  
  const loadMore = () => {
    if (hasMore && !isLoading) {
      setCurrentPage(prev => prev + 1);
    }
  };
  
  // Reset pagination when filters change
  const filtersString = JSON.stringify(filters);
  React.useEffect(() => {
    setCurrentPage(0);
  }, [filtersString]);

  // Handle results modal
  const handleShowResults = (tournament: Tournament) => {
    setSelectedTournamentForResults(tournament);
    setShowResultsModal(true);
  };

  const handleCloseResults = () => {
    setShowResultsModal(false);
    setSelectedTournamentForResults(null);
  };
  
  if (isLoading && tournaments.length === 0) {
    return (
      <div className="bg-black/20 backdrop-blur-sm rounded-xl border border-blue-500/30 p-12 text-center">
        <Loader2 size={48} className="mx-auto text-blue-400 mb-4 animate-spin" />
        <h3 className="text-xl font-bold text-white mb-2">Caricamento tornei...</h3>
        <p className="text-blue-300">
          Attendere il caricamento dei tornei.
        </p>
      </div>
    );
  }
  
  if (!tournaments.length && !isLoading) {
    return (
      <div className="bg-black/20 backdrop-blur-sm rounded-xl border border-blue-500/30 p-12 text-center">
        <CalendarIcon size={48} className="mx-auto text-blue-400 mb-4" />
        <h3 className="text-xl font-bold text-white mb-2">Nessun torneo disponibile</h3>
        <p className="text-blue-300">
          {filters && (filters.startDate || filters.endDate || filters.storeId || filters.sortOrder !== 'nearest-first') 
            ? 'Non ci sono tornei che corrispondono ai filtri selezionati.' 
            : 'Non ci sono tornei programmati al momento.'}
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        {/* Cards Layout */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {tournaments.map((tournament) => (
            <TournamentCard
              key={tournament.id}
              tournament={tournament}
              onRegistrationClick={onRegistrationClick}
              onEditClick={onEditClick}
              onTournamentClick={onTournamentClick}
              onShowResults={handleShowResults}
              isSelected={selectedTournament?.id === tournament.id}
              disableSelection={true}
            />
          ))}
        </div>
      
        {/* Load More Button */}
        {hasMore && (
          <div className="flex justify-center pt-4">
            <button
              onClick={loadMore}
              disabled={isLoading}
              className="
                inline-flex items-center gap-2 px-6 py-3
                bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800
                text-white font-medium rounded-lg
                transition-colors duration-200
                disabled:cursor-not-allowed
              "
            >
              {isLoading ? (
                <>
                  <Loader2 size={16} className="animate-spin" />
                  Caricamento...
                </>
              ) : (
                'Carica altri tornei'
              )}
            </button>
          </div>
        )}
      </div>

      {/* Results Modal - Rendered at top level */}
      {showResultsModal && selectedTournamentForResults && (
        <TournamentResultsModal
          tournament={selectedTournamentForResults}
          isOpen={showResultsModal}
          onClose={handleCloseResults}
        />
      )}
    </>
  );
}
