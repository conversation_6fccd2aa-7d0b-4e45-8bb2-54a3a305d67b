"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Play, Pause, RotateCcw, Clock } from "lucide-react";

interface TournamentTimerProps {
  durationMinutes: number;
  onTimeUp?: () => void;
  onTimeChange?: (remainingSeconds: number) => void;
  className?: string;
}

export function TournamentTimer({
  durationMinutes,
  onTimeUp,
  onTimeChange,
  className = ""
}: TournamentTimerProps) {
  const [remainingSeconds, setRemainingSeconds] = useState(durationMinutes * 60);
  const [isRunning, setIsRunning] = useState(false);
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);

  // Calculate total duration in seconds
  const totalSeconds = durationMinutes * 60;

  // Format time as MM:SS
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Get color based on remaining time
  const getTimerColor = (): string => {
    const percentage = remainingSeconds / totalSeconds;
    if (percentage > 0.5) return 'text-green-400'; // Green for > 50%
    if (percentage > 0.25) return 'text-yellow-400'; // Yellow for 25-50%
    return 'text-red-400'; // Red for < 25%
  };

  // Get background color for the timer display
  const getTimerBgColor = (): string => {
    const percentage = remainingSeconds / totalSeconds;
    if (percentage > 0.5) return 'bg-green-900/20 border-green-500/30';
    if (percentage > 0.25) return 'bg-yellow-900/20 border-yellow-500/30';
    return 'bg-red-900/20 border-red-500/30';
  };

  // Timer tick function
  const tick = useCallback(() => {
    setRemainingSeconds(prev => {
      const newValue = Math.max(0, prev - 1);
      
      // Call onTimeChange callback
      if (onTimeChange) {
        onTimeChange(newValue);
      }
      
      // Check if time is up
      if (newValue === 0) {
        setIsRunning(false);
        if (onTimeUp) {
          onTimeUp();
        }
      }
      
      return newValue;
    });
  }, [onTimeChange, onTimeUp]);

  // Effect to handle timer interval
  useEffect(() => {
    if (isRunning && remainingSeconds > 0) {
      const id = setInterval(tick, 1000);
      setIntervalId(id);
      return () => {
        clearInterval(id);
        setIntervalId(null);
      };
    } else {
      if (intervalId) {
        clearInterval(intervalId);
        setIntervalId(null);
      }
    }
  }, [isRunning, remainingSeconds, tick]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [intervalId]);

  // Reset timer when duration changes
  useEffect(() => {
    setRemainingSeconds(durationMinutes * 60);
    setIsRunning(false);
  }, [durationMinutes]);

  const handleStart = () => {
    if (remainingSeconds > 0) {
      setIsRunning(true);
    }
  };

  const handlePause = () => {
    setIsRunning(false);
  };

  const handleReset = () => {
    setIsRunning(false);
    setRemainingSeconds(totalSeconds);
    if (onTimeChange) {
      onTimeChange(totalSeconds);
    }
  };

  const isTimeUp = remainingSeconds === 0;
  const canStart = !isRunning && remainingSeconds > 0;
  const canPause = isRunning;

  return (
    <div className={`flex items-center justify-center gap-6 ${className}`}>
      {/* Timer Display - Compact */}
      <div className={`
        relative px-8 py-4 rounded-xl border-2 transition-all duration-300
        ${getTimerBgColor()}
        ${isTimeUp ? 'animate-pulse' : ''}
      `}>
        <div className="flex items-center gap-3">
          <Clock size={28} className={getTimerColor()} />
          <div className={`
            text-4xl sm:text-5xl font-mono font-bold transition-colors duration-300
            ${getTimerColor()}
          `}>
            {formatTime(remainingSeconds)}
          </div>
        </div>

        {/* Progress bar */}
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/20 rounded-b-xl overflow-hidden">
          <div
            className={`h-full transition-all duration-1000 ${
              remainingSeconds / totalSeconds > 0.5 ? 'bg-green-400' :
              remainingSeconds / totalSeconds > 0.25 ? 'bg-yellow-400' : 'bg-red-400'
            }`}
            style={{ width: `${(remainingSeconds / totalSeconds) * 100}%` }}
          />
        </div>
      </div>

      {/* Timer Controls - Horizontal */}
      <div className="flex items-center gap-3">
        <button
          onClick={handleStart}
          disabled={!canStart}
          className={`
            p-3 rounded-lg font-medium transition-all duration-200
            flex items-center justify-center
            ${canStart
              ? 'bg-green-600 hover:bg-green-700 hover:scale-105 active:scale-95'
              : 'bg-gray-600 cursor-not-allowed opacity-50'
            }
            focus:outline-none focus:ring-2 focus:ring-green-500/50
          `}
          title="Avvia timer"
        >
          <Play size={20} className="text-white" />
        </button>

        <button
          onClick={handlePause}
          disabled={!canPause}
          className={`
            p-3 rounded-lg font-medium transition-all duration-200
            flex items-center justify-center
            ${canPause
              ? 'bg-yellow-600 hover:bg-yellow-700 hover:scale-105 active:scale-95'
              : 'bg-gray-600 cursor-not-allowed opacity-50'
            }
            focus:outline-none focus:ring-2 focus:ring-yellow-500/50
          `}
          title="Pausa timer"
        >
          <Pause size={20} className="text-white" />
        </button>

        <button
          onClick={handleReset}
          className="
            p-3 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium
            transition-all duration-200 flex items-center justify-center
            hover:scale-105 active:scale-95
            focus:outline-none focus:ring-2 focus:ring-blue-500/50
          "
          title="Reset timer"
        >
          <RotateCcw size={20} className="text-white" />
        </button>
      </div>

      {/* Timer Status - Compact */}
      <div className="text-center">
        <div className={`text-sm font-medium ${
          isTimeUp ? 'text-red-400' :
          isRunning ? 'text-green-400' : 'text-blue-300'
        }`}>
          {isTimeUp ? 'Tempo scaduto!' :
           isRunning ? 'Timer in corso...' : 'Timer in pausa'}
        </div>
        <div className="text-xs text-blue-400 mt-1">
          Durata: {durationMinutes} min
        </div>
      </div>
    </div>
  );
}
