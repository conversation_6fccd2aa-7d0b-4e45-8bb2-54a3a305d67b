"use client";

import React, { useState } from "react";
import { Users, Hash, <PERSON>, Shuffle, Check } from "lucide-react";
import { PlayerWithStats, PairingWithPlayers, getPlayerDisplayName } from "@/lib/mock/tournamentOrganizerMock";

interface PairingInterfaceProps {
  players: PlayerWithStats[];
  currentRound: number;
  onGeneratePairings?: () => void;
  pairings: PairingWithPlayers[];
  pairingStatus: 'none' | 'generated' | 'confirmed';
  className?: string;
  isHistorical?: boolean;
}

export function PairingInterface({
  players,
  currentRound,
  onGeneratePairings,
  pairings,
  pairingStatus,
  className = "",
  isHistorical = false
}: PairingInterfaceProps) {
  const [selectedPairing, setSelectedPairing] = useState<string | null>(null);

  const renderPairingCard = (pairing: PairingWithPlayers, index: number) => {
    const isSelected = selectedPairing === pairing.id;
    
    return (
      <div
        key={pairing.id}
        className={`
          bg-blue-900/20 border rounded-lg p-4 transition-all duration-200
          ${isSelected ? 'border-blue-400 bg-blue-900/30' : 'border-blue-500/30'}
          ${pairingStatus === 'confirmed' ? 'opacity-75' : 'hover:border-blue-400/50 cursor-pointer'}
        `}
        onClick={() => pairingStatus !== 'confirmed' && setSelectedPairing(isSelected ? null : pairing.id)}
      >
        {/* Table Number */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            {pairing.is_bye ? (
              <Coffee size={16} className="text-yellow-400" />
            ) : (
              <Hash size={16} className="text-blue-400" />
            )}
            <span className="text-sm font-medium text-blue-200">
              {pairing.is_bye ? 'BYE' : `Tavolo ${pairing.table_number || index + 1}`}
            </span>
          </div>
          
          {pairingStatus === 'confirmed' && (
            <Check size={16} className="text-green-400" />
          )}
        </div>

        {/* Players */}
        <div className="space-y-2">
          {/* Player 1 */}
          <div className="bg-black/20 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-white text-sm">
                  {getPlayerDisplayName(pairing.player1)}
                </div>
                <div className="text-xs text-blue-300">
                  {pairing.player1.points} punti • {pairing.player1.wins}W-{pairing.player1.losses}L
                </div>
              </div>
            </div>
          </div>

          {/* VS or BYE indicator */}
          <div className="text-center">
            {pairing.is_bye ? (
              <div className="text-yellow-400 text-xs font-medium">BYE</div>
            ) : (
              <div className="text-blue-400 text-xs font-medium">VS</div>
            )}
          </div>

          {/* Player 2 (if not bye) */}
          {!pairing.is_bye && pairing.player2 && (
            <div className="bg-black/20 rounded-lg p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-white text-sm">
                    {getPlayerDisplayName(pairing.player2)}
                  </div>
                  <div className="text-xs text-blue-300">
                    {pairing.player2.points} punti • {pairing.player2.wins}W-{pairing.player2.losses}L
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Result (if confirmed) */}
        {pairingStatus === 'confirmed' && pairing.result && (
          <div className="mt-3 pt-3 border-t border-blue-500/20">
            <div className="text-center text-sm text-green-400">
              Risultato: {pairing.result}
            </div>
          </div>
        )}
      </div>
    );
  };

  if (pairingStatus === 'none') {
    return (
      <div className={`flex flex-col items-center justify-center h-full ${className}`}>
        <div className="text-center space-y-6 max-w-md mx-auto">
          <Users size={48} className="text-blue-400 mx-auto" />
          <div>
            <h3 className="text-lg font-semibold text-white mb-2">
              Round {currentRound} - Accoppiamenti
            </h3>
            <p className="text-blue-300 text-sm mb-4">
              Genera gli accoppiamenti per iniziare il round
            </p>
            <div className="text-xs text-blue-400 mb-6">
              Giocatori attivi: {players.length}
            </div>
          </div>

          {!isHistorical && onGeneratePairings && (
            <button
              onClick={onGeneratePairings}
              className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors flex items-center gap-2 mx-auto"
            >
              <Shuffle size={20} />
              Genera Accoppiamenti
            </button>
          )}

          {isHistorical && (
            <div className="text-center text-blue-300 text-sm">
              Visualizzazione storica - Round completato
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h3 className="text-lg font-semibold text-white">
              Round {currentRound} - Accoppiamenti
            </h3>
            <div className="text-sm text-blue-300">
              {pairings.length} {pairings.length === 1 ? 'accoppiamento' : 'accoppiamenti'} •
              {pairings.filter(p => p.is_bye).length > 0 && ` ${pairings.filter(p => p.is_bye).length} bye`}
            </div>
          </div>
        </div>

        {/* Generate Pairings Button - Under Header (only for historical view with no pairings) */}
        {!isHistorical && onGeneratePairings && pairings.length === 0 && (
          <div className="mb-4">
            <button
              onClick={onGeneratePairings}
              className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors flex items-center gap-2"
            >
              <Shuffle size={20} />
              Genera Accoppiamenti
            </button>
          </div>
        )}
      </div>

      {/* Pairings Grid */}
      <div className="flex-1 overflow-y-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
          {pairings.map((pairing, index) => renderPairingCard(pairing, index))}
        </div>
      </div>
    </div>
  );
}
