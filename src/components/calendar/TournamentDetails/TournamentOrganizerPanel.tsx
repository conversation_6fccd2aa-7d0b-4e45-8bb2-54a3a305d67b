"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>tings, Users, RotateCcw, Trophy, ChevronLeft, ChevronRight } from "lucide-react";
import { useLockBodyScroll } from "@/lib/hooks/useLockBodyScroll";
import { Tournament } from "@/types/calendar";
import { TournamentSettings, TournamentSetupModal } from "./TournamentSetupModal";
import { TournamentTimer } from "./TournamentTimer";
import { PairingInterface } from "./PairingInterface";
import { ResultsInterface } from "./ResultsInterface";
import { TournamentOrganizerService } from "@/lib/services/tournamentOrganizerMock";
import { generateSwissPairings } from "@/lib/utils/swissPairing";
import { RoundManager } from "@/lib/utils/roundManagement";
import { TournamentScoring } from "@/lib/utils/tournamentScoring";
import {
  PlayerWithStats,
  PairingWithPlayers,
  TournamentPlayerStats,
  mockDataState,
  getPlayerDisplayName
} from "@/lib/mock/tournamentOrganizerMock";

interface TournamentOrganizerPanelProps {
  isOpen: boolean;
  onClose: () => void;
  tournament: Tournament;
  settings: TournamentSettings;
  onSettingsChange: (settings: TournamentSettings) => void;
}

export function TournamentOrganizerPanel({
  isOpen,
  onClose,
  tournament,
  settings,
  onSettingsChange
}: TournamentOrganizerPanelProps) {
  const [showSettingsModal, setShowSettingsModal] = useState(false);

  // Lock body scroll when modal is open
  useLockBodyScroll(isOpen);

  // Handle escape key
  useEffect(() => {
    if (!isOpen) return;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  // Handle settings save
  const handleSaveSettings = (newSettings: TournamentSettings) => {
    onSettingsChange(newSettings);
    setShowSettingsModal(false);
  };

  const [currentRound, setCurrentRound] = useState(1);
  const [viewingRound, setViewingRound] = useState(1); // Round currently being viewed (can be different from current)
  const [roundStatus, setRoundStatus] = useState<'pending' | 'active' | 'completed'>('pending');
  const [pairingStatus, setPairingStatus] = useState<'none' | 'generated' | 'confirmed'>('none');
  const [pairings, setPairings] = useState<any[]>([]);
  const [currentView, setCurrentView] = useState<'pairings' | 'results'>('pairings');
  const [completedRounds, setCompletedRounds] = useState<number[]>([]); // Track which rounds are completed

  // Determine if tournament has started (any round has been confirmed)
  const tournamentStarted = pairingStatus === 'confirmed' || currentRound > 1;

  // Get sorted players (by ranking if tournament started, otherwise original order)
  const getSortedPlayers = () => {
    if (!tournamentStarted) {
      return players;
    }

    // Sort by points (descending), then by wins (descending), then by losses (ascending)
    return [...players].sort((a, b) => {
      if (a.points !== b.points) return b.points - a.points;
      if (a.wins !== b.wins) return b.wins - a.wins;
      return a.losses - b.losses;
    });
  };

  // Tournament data
  const [players, setPlayers] = useState<PlayerWithStats[]>([]);
  const [playerStats, setPlayerStats] = useState<TournamentPlayerStats[]>([]);
  const [allPairings, setAllPairings] = useState<PairingWithPlayers[]>([]);

  // Load initial data
  useEffect(() => {
    const loadTournamentData = async () => {
      const playersResponse = await TournamentOrganizerService.getPlayers(tournament.id);
      const statsResponse = await TournamentOrganizerService.playerStats.getByTournamentId(tournament.id);

      if (playersResponse.data) setPlayers(playersResponse.data);
      if (statsResponse.data) setPlayerStats(statsResponse.data);
    };

    if (isOpen) {
      loadTournamentData();
    }
  }, [isOpen, tournament.id]);

  // Pairing handlers
  const handleGeneratePairings = async () => {
    try {
      const roundId = `round-${currentRound}`;

      // Generate Swiss pairings using the algorithm
      const pairingResult = generateSwissPairings({
        roundId,
        roundNumber: currentRound,
        players,
        previousPairings: allPairings,
        playerStats
      });

      if (!pairingResult.success) {
        console.error('Failed to generate pairings:', pairingResult.error);
        return;
      }

      // Create pairings in the service
      const createResponse = await TournamentOrganizerService.pairings.createBatch(pairingResult.pairings);

      if (createResponse.data) {
        setPairings(createResponse.data);
        setAllPairings(prev => [...prev, ...createResponse.data]);
        setPairingStatus('generated');
      }
    } catch (error) {
      console.error('Error generating pairings:', error);
    }
  };

  const handleConfirmPairings = () => {
    setPairingStatus('confirmed');
    setRoundStatus('active');
    setCurrentView('results'); // Switch to results tab automatically
  };

  const handleCancelPairings = () => {
    setPairings([]);
    setPairingStatus('none');
  };



  const handleCompleteRound = async () => {
    try {
      // Calculate and update player statistics
      const standings = TournamentScoring.calculateStandings(players, allPairings);

      // Update player stats in the service
      const updatePromises = standings.map(standing => {
        return TournamentOrganizerService.playerStats.updatePlayerStats(
          standing.playerId,
          tournament.id,
          {
            match_points: standing.matchPoints,
            game_wins: standing.gameWins,
            game_losses: standing.gameLosses,
            matches_played: standing.matchesPlayed,
            omw_percentage: standing.omwPercentage,
            gwp_percentage: standing.gwpPercentage
          }
        );
      });

      await Promise.all(updatePromises);

      // Mark current round as completed
      setCompletedRounds(prev => [...prev, currentRound]);
      setRoundStatus('completed');
      setCurrentView('pairings');

      // Move to next round if not the last round
      if (currentRound < settings.rounds) {
        const nextRound = currentRound + 1;
        setCurrentRound(nextRound);
        setViewingRound(nextRound); // Also update viewing round to the new current round
        setPairingStatus('none');
        setPairings([]);
      }

      console.log('Round completed, standings updated');
    } catch (error) {
      console.error('Error completing round:', error);
    }
  };

  // Round navigation functions
  const handlePreviousRound = () => {
    if (viewingRound > 1) {
      setViewingRound(viewingRound - 1);
      // Load pairings for the previous round
      // TODO: Implement loading historical pairings
    }
  };

  const handleNextRound = () => {
    if (viewingRound < currentRound) {
      setViewingRound(viewingRound + 1);
      // Load pairings for the next round
      // TODO: Implement loading historical pairings
    }
  };

  const isViewingCurrentRound = viewingRound === currentRound;
  const isViewingCompletedRound = completedRounds.includes(viewingRound);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Overlay */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" aria-hidden="true" />

      {/* Modal Content */}
      <div className="relative w-full h-full bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 overflow-hidden">
        <div className="h-full flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-blue-500/20 bg-black/20">
          {/* Left: Tournament Info */}
          <div className="flex items-center gap-4 flex-1">
            <h1 className="text-xl sm:text-2xl font-bold text-white">
              Tournament Organizer
            </h1>
            <div className="text-sm text-blue-300">
              {tournament.title}
            </div>
          </div>

          {/* Center: Round Info */}
          <div className="flex-1 flex justify-center">
            <div className="text-center text-white">
              <div className="flex items-center justify-center gap-3 mb-1">
                <h2 className="text-lg font-semibold">
                  Round {currentRound} di {settings.rounds}
                </h2>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  roundStatus === 'pending' ? 'bg-yellow-600 text-yellow-100' :
                  roundStatus === 'active' ? 'bg-green-600 text-green-100' :
                  'bg-blue-600 text-blue-100'
                }`}>
                  {roundStatus === 'pending' ? 'In attesa' :
                   roundStatus === 'active' ? 'In corso' : 'Completato'}
                </span>
              </div>
              <p className="text-blue-200 text-sm">
                Durata: {settings.roundDurationMinutes} minuti
              </p>
            </div>
          </div>

          {/* Right: Action Buttons */}
          <div className="flex items-center gap-2 flex-1 justify-end">
            {/* Settings Button */}
            <button
              onClick={() => setShowSettingsModal(true)}
              className="p-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
              title="Impostazioni"
            >
              <Settings size={20} className="text-white" />
            </button>

            {/* Close Button */}
            <button
              onClick={onClose}
              className="p-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
              title="Chiudi"
            >
              <X size={20} className="text-white" />
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden min-h-0">
          {/* Left Sidebar - Player List / Standings */}
          <div className="w-80 border-r border-blue-500/20 bg-black/10 flex flex-col">
            <div className="p-4 border-b border-blue-500/20">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-lg font-semibold text-white flex items-center gap-2">
                  {tournamentStarted ? (
                    <>
                      <Trophy size={18} />
                      Classifica Attuale ({players.length})
                    </>
                  ) : (
                    <>
                      <Users size={18} />
                      Giocatori ({players.length})
                    </>
                  )}
                </h2>
                <button className="p-1 hover:bg-blue-900/50 rounded">
                  <RotateCcw size={16} className="text-blue-300" />
                </button>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto p-4">
              <div className="space-y-2">
                {getSortedPlayers().map((player, index) => (
                  <div
                    key={player.id}
                    className={`
                      bg-blue-900/20 border border-blue-500/30 rounded-lg p-3
                      ${tournamentStarted && index === 0 ? 'bg-yellow-600/20 border-yellow-500/30' :
                        tournamentStarted && index === 1 ? 'bg-gray-600/20 border-gray-500/30' :
                        tournamentStarted && index === 2 ? 'bg-orange-600/20 border-orange-500/30' : ''
                      }
                    `}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-white text-sm">
                          {index + 1}. {getPlayerDisplayName(player)}
                          {tournamentStarted && index === 0 && (
                            <span className="ml-2 text-yellow-400">👑</span>
                          )}
                        </div>
                        <div className="text-xs text-blue-300">
                          {player.points} punti • {player.wins}W-{player.losses}L
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Center - Pairings/Matches */}
          <div className="flex-1 flex flex-col min-h-0 overflow-hidden">


            {/* Timer Section - Only show when round is confirmed/active */}
            {pairingStatus === 'confirmed' && (
              <div className="p-4 border-b border-blue-500/20 bg-black/5">
                <TournamentTimer
                  durationMinutes={settings.roundDurationMinutes}
                  onTimeUp={() => {
                    console.log('Round time is up!');
                    // TODO: Handle round time up
                  }}
                  onTimeChange={(remainingSeconds) => {
                    // TODO: Update round timer state
                  }}
                />
              </div>
            )}

            {/* Controls Section */}
            <div className="p-4 border-b border-blue-500/20 bg-black/5">
              {/* Round Navigation */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <button
                    onClick={handlePreviousRound}
                    disabled={viewingRound <= 1}
                    className={`p-2 rounded-lg transition-colors ${
                      viewingRound <= 1
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : 'bg-blue-900/30 text-blue-300 hover:bg-blue-900/50'
                    }`}
                  >
                    <ChevronLeft size={20} />
                  </button>

                  <div className="text-center">
                    <h3 className="text-lg font-semibold text-white">
                      Round {viewingRound}
                      {!isViewingCurrentRound && (
                        <span className="text-sm text-blue-300 ml-2">(Storico)</span>
                      )}
                      {isViewingCompletedRound && (
                        <span className="text-sm text-green-400 ml-2">✓ Completato</span>
                      )}
                    </h3>
                    <div className="text-xs text-blue-300">
                      Round {viewingRound} di {settings.rounds}
                    </div>
                  </div>

                  <button
                    onClick={handleNextRound}
                    disabled={viewingRound >= currentRound}
                    className={`p-2 rounded-lg transition-colors ${
                      viewingRound >= currentRound
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : 'bg-blue-900/30 text-blue-300 hover:bg-blue-900/50'
                    }`}
                  >
                    <ChevronRight size={20} />
                  </button>
                </div>

                {/* Round Status Info */}
                <div className="text-right text-xs text-blue-300">
                  <p>Round corrente: {currentRound}</p>
                  <p>Round completati: {completedRounds.length}</p>
                </div>
              </div>

              <div className="flex items-center justify-between mb-4">
                {/* View Switcher and Action Buttons */}
                <div className="flex items-center gap-4">
                  {/* View Switcher */}
                  <div className="flex gap-2">
                    <button
                      onClick={() => setCurrentView('pairings')}
                      disabled={!isViewingCurrentRound && pairingStatus === 'confirmed'}
                      className={`py-2 px-4 rounded-lg text-sm font-medium transition-colors ${
                        currentView === 'pairings'
                          ? 'bg-blue-600 text-white'
                          : (!isViewingCurrentRound && pairingStatus === 'confirmed')
                            ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                            : 'bg-blue-900/30 text-blue-300 hover:bg-blue-900/50'
                      }`}
                    >
                      Accoppiamenti
                    </button>
                    <button
                      onClick={() => setCurrentView('results')}
                      disabled={!isViewingCompletedRound && pairingStatus !== 'confirmed'}
                      className={`py-2 px-4 rounded-lg text-sm font-medium transition-colors ${
                        currentView === 'results'
                          ? 'bg-blue-600 text-white'
                          : (isViewingCompletedRound || pairingStatus === 'confirmed')
                            ? 'bg-blue-900/30 text-blue-300 hover:bg-blue-900/50'
                            : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      }`}
                    >
                      Risultati
                    </button>
                  </div>

                  {/* Action Buttons - Same level as View Switcher */}
                  {currentView === 'pairings' && pairingStatus === 'generated' && (
                    <div className="flex gap-3">
                      <button
                        onClick={handleConfirmPairings}
                        className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
                      >
                        Conferma Round
                      </button>

                      <button
                        onClick={handleCancelPairings}
                        className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors"
                      >
                        Annulla Accoppiamenti
                      </button>
                    </div>
                  )}

                  {currentView === 'pairings' && pairingStatus === 'confirmed' && (
                    <div className="flex items-center gap-2 text-green-400">
                      <span className="text-sm font-medium">Round confermato</span>
                      <span className="text-xs text-blue-300">• Vai a "Risultati" per inserire i risultati</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Main Content - Pairings or Results */}
            <div className="flex-1 overflow-hidden p-4 min-h-0">
              {currentView === 'pairings' ? (
                <PairingInterface
                  players={players}
                  currentRound={viewingRound}
                  onGeneratePairings={isViewingCurrentRound ? handleGeneratePairings : undefined}
                  pairings={pairings}
                  pairingStatus={isViewingCurrentRound ? pairingStatus : 'confirmed'}
                  className="h-full"
                  isHistorical={!isViewingCurrentRound}
                />
              ) : (
                <ResultsInterface
                  pairings={pairings}
                  onCompleteRound={isViewingCurrentRound ? handleCompleteRound : undefined}
                  className="h-full"
                  isHistorical={!isViewingCurrentRound}
                />
              )}
            </div>
          </div>


        </div>
        </div>
      </div>

      {/* Settings Modal */}
      {showSettingsModal && (
        <TournamentSetupModal
          isOpen={showSettingsModal}
          onClose={() => setShowSettingsModal(false)}
          onSave={handleSaveSettings}
          initialSettings={settings}
        />
      )}
    </div>
  );
}
