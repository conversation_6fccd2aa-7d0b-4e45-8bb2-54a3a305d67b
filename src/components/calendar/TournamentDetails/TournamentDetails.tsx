"use client";

import React, { useEffect, useState } from "react";
import { Tournament } from "@/types/calendar";
import { Colors } from "@/lib/constants/colors";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { Plus, Eye } from "lucide-react";
import { TournamentResultsModal } from "./TournamentResultsModal";
import { TournamentInfo } from "./TournamentInfo";
import { TournamentActions } from "./TournamentActions";
import { useTournamentDetails } from "./useTournamentDetails";
import { useTournament } from "@/lib/hooks/useTournaments";
import { TournamentRegistrationsModal } from "./TournamentRegistrationsModal";
import { ManageRegistrationModal } from "./ManageRegistrationModal";
import { AdminRecordResultsModal } from "./AdminRecordResultsModal";
import { TournamentCreationModal } from "./TournamentCreationModal";
import { TournamentSetupModal, TournamentSettings } from "./TournamentSetupModal";
import { TournamentOrganizerPanel } from "./TournamentOrganizerPanel";

interface TournamentDetailsProps {
  selectedTournament: Tournament | null;
  selectedDate: Date | null;
  onRegistrationClick: () => void;
  onEditClick?: (tournament: Tournament) => void;
  onTournamentUpdate?: (tournament: Tournament) => void;
}

export function TournamentDetails({
  selectedTournament,
  selectedDate,
  onRegistrationClick,
  onEditClick,
  onTournamentUpdate
}: TournamentDetailsProps) {
  const {
    isAdmin,
    isAdminLoading,
    showResultsModal,
    showRegistrationsModal,
    showManageRegistrationModal,
    showRecordResultsModal,
    isEventPast,
    handleDeleteTournament,
    handleEditClick,
    handleTournamentAction,
    openRegistrationsModal,
    openManageRegistrationModal,
    openRecordResultsModal,
    closeResultsModal,
    closeRegistrationsModal,
    closeManageRegistrationModal,
    closeRecordResultsModal
  } = useTournamentDetails(selectedTournament, onRegistrationClick, onEditClick);

  // Tournament organizer state (local state for now, will be moved to database later)
  const [hasOrganizer, setHasOrganizer] = useState(false);
  const [showCreationModal, setShowCreationModal] = useState(false);
  const [showSetupModal, setShowSetupModal] = useState(false);
  const [showOrganizerPanel, setShowOrganizerPanel] = useState(false);
  const [tournamentSettings, setTournamentSettings] = useState<TournamentSettings | null>(null);

  // Handler for creating tournament organizer
  const handleCreateTournamentOrganizer = () => {
    // Show setup modal for first-time configuration
    setShowSetupModal(true);
  };

  // Handler for saving tournament settings
  const handleSaveTournamentSettings = (settings: TournamentSettings) => {
    setTournamentSettings(settings);
    setHasOrganizer(true);
    console.log('Tournament organizer created with settings:', settings);
  };

  // Hook deve essere chiamato sempre, non condizionatamente
  const { data: freshTournament } = useTournament(selectedTournament?.id || '');

  // Aggiorna il torneo selezionato quando arrivano dati freschi
  useEffect(() => {
    if (freshTournament && onTournamentUpdate && selectedTournament?.id === freshTournament.id) {
      onTournamentUpdate(freshTournament);
    }
  }, [freshTournament, onTournamentUpdate, selectedTournament?.id]);

  if (selectedTournament) {
    // Usa dati aggiornati se disponibili, altrimenti quelli originali
    const tournamentData = freshTournament || selectedTournament;
    
    const storeColor = Colors.getStoreScheme(tournamentData.store, tournamentData);
    const currentPlayers = tournamentData.tournament_registrations?.[0]?.count || 0;
    const isPastEvent = isEventPast(tournamentData.date);
    
    return (
      <>
        <div className={`bg-black/20 backdrop-blur-sm rounded-xl border ${storeColor.border} p-4 sm:p-6 h-full`}>
          {/* Header with title and tournament organizer button */}
          <div className="flex items-start justify-between mb-3 sm:mb-4">
            <h3 className="text-lg sm:text-xl font-bold">{tournamentData.title}</h3>

            {/* Tournament Organizer Button - Admin Only */}
            {isAdmin && !isAdminLoading && (
              <div className="relative group">
                <button
                  onClick={() => {
                    if (hasOrganizer) {
                      setShowOrganizerPanel(true);
                    } else {
                      setShowCreationModal(true);
                    }
                  }}
                  className={`
                    w-8 h-8 sm:w-10 sm:h-10 rounded-lg font-medium transition-all duration-200
                    flex items-center justify-center
                    ${hasOrganizer
                      ? 'bg-blue-600 hover:bg-blue-700'
                      : 'bg-green-600 hover:bg-green-700'
                    }
                    hover:scale-105 active:scale-95
                    focus:outline-none focus:ring-2 focus:ring-blue-500/50
                  `}
                  title={hasOrganizer ? "view tournament" : "create tournament"}
                >
                  {hasOrganizer ? (
                    <Eye size={16} className="text-white" />
                  ) : (
                    <Plus size={16} className="text-white" />
                  )}
                </button>

                {/* Tooltip */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/80 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                  {hasOrganizer ? "view tournament" : "create tournament"}
                </div>
              </div>
            )}
          </div>
          
          <TournamentInfo 
            tournament={tournamentData}
            storeColor={storeColor}
            currentPlayers={currentPlayers}
          />
          
          <TournamentActions
            tournament={tournamentData}
            storeColor={storeColor}
            isAdmin={isAdmin}
            isAdminLoading={isAdminLoading}
            isPastEvent={isPastEvent}
            currentPlayers={currentPlayers}
            onEditClick={handleEditClick}
            onDeleteClick={handleDeleteTournament}
            onTournamentAction={handleTournamentAction}
            onViewRegistrations={openRegistrationsModal}
            onPlayerRegistrationClick={onRegistrationClick}
            onManageRegistration={openManageRegistrationModal}
            onRecordResults={openRecordResultsModal}
          />
        </div>
        
        {/* Modale per i risultati degli eventi passati */}
        {showResultsModal && (
          <TournamentResultsModal 
            tournament={tournamentData}
            isOpen={showResultsModal}
            onClose={closeResultsModal}
          />
        )}

        {/* Modale iscritti */}
        {showRegistrationsModal && (
          <TournamentRegistrationsModal
            tournament={tournamentData}
            isOpen={showRegistrationsModal}
            onClose={closeRegistrationsModal}
          />
        )}

        {/* Modale gestione iscrizione */}
        {showManageRegistrationModal && (
          <ManageRegistrationModal
            tournament={tournamentData}
            isOpen={showManageRegistrationModal}
            onClose={closeManageRegistrationModal}
          />
        )}

        {/* Modale registrazione risultati (admin only) */}
        {showRecordResultsModal && (
          <AdminRecordResultsModal
            tournament={tournamentData}
            isOpen={showRecordResultsModal}
            onClose={closeRecordResultsModal}
          />
        )}

        {/* Modale creazione tournament organizer (admin only) */}
        {showCreationModal && (
          <TournamentCreationModal
            isOpen={showCreationModal}
            onClose={() => setShowCreationModal(false)}
            onConfirm={handleCreateTournamentOrganizer}
            tournamentTitle={tournamentData.title}
          />
        )}

        {/* Modale configurazione tournament organizer (admin only) */}
        {showSetupModal && (
          <TournamentSetupModal
            isOpen={showSetupModal}
            onClose={() => setShowSetupModal(false)}
            onSave={handleSaveTournamentSettings}
            initialSettings={tournamentSettings || undefined}
          />
        )}

        {/* Tournament Organizer Panel (admin only) */}
        {showOrganizerPanel && hasOrganizer && tournamentSettings && (
          <TournamentOrganizerPanel
            isOpen={showOrganizerPanel}
            onClose={() => setShowOrganizerPanel(false)}
            tournament={tournamentData}
            settings={tournamentSettings}
            onSettingsChange={setTournamentSettings}
          />
        )}
      </>
    );
  }
  
  if (selectedDate) {
    return (
      <div className="bg-black/20 backdrop-blur-sm rounded-xl border border-blue-500/30 p-4 sm:p-6 h-full">
        <h3 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4">
          {format(selectedDate, "EEEE d MMMM yyyy", { locale: it })}
        </h3>
        
        <p className="text-blue-300 text-sm sm:text-base">Nessun torneo programmato per questa data.</p>
      </div>
    );
  }
  
  return (
    <div className="bg-black/20 backdrop-blur-sm rounded-xl border border-blue-500/30 p-4 sm:p-6 h-full">
      <h3 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4">Dettagli Torneo</h3>
      <p className="text-blue-300 text-sm sm:text-base">Seleziona una data nel calendario per visualizzare i dettagli del torneo.</p>
    </div>
  );
}
