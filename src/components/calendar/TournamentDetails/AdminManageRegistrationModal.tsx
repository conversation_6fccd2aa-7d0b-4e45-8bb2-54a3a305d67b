"use client";

import React, { useState, useEffect } from "react";
import { X, Check, AlertCircle, Save, Trash2, User } from "lucide-react";
import { useArchetypes } from "@/lib/hooks/useArchetypes";
import { useLockBodyScroll } from "@/lib/hooks/useLockBodyScroll";
import { useAdminRegistration, useAdminUpdateRegistration, useAdminDeleteRegistration } from "@/lib/hooks/useRegistration";
import { Select } from "@/components/ui/Select";
import { validateManaboxUrl, formatManaboxUrl } from "@/lib/utils/manaboxValidation";

interface AdminManageRegistrationModalProps {
  registrationId: string;
  isOpen: boolean;
  onClose: () => void;
}

export function AdminManageRegistrationModal({
  registrationId,
  isOpen,
  onClose
}: AdminManageRegistrationModalProps) {
  const { data: archetypes, isLoading: isLoadingArchetypes } = useArchetypes();
  const { data: registration, isLoading: isLoadingRegistration } = useAdminRegistration(registrationId);
  const { mutateAsync: updateRegistration, isPending: isUpdating } = useAdminUpdateRegistration();
  const { mutateAsync: deleteRegistration, isPending: isDeleting } = useAdminDeleteRegistration();

  // Lock body scroll when modal is open
  useLockBodyScroll(isOpen);

  const [formData, setFormData] = useState({
    deckName: "",
    archetype: "",
    decklist: ""
  });
  const [errors, setErrors] = useState({
    deckName: false,
    archetype: false,
    decklist: false
  });
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Populate form when registration data loads
  useEffect(() => {
    if (registration) {
      setFormData({
        deckName: registration.deck_name || "",
        archetype: registration.archetype_id || "",
        decklist: registration.deck_list_url || ""
      });
    }
  }, [registration]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setIsSubmitted(false);
      setShowDeleteConfirm(false);
      setErrors({
        deckName: false,
        archetype: false,
        decklist: false
      });
    }
  }, [isOpen]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [field]: false }));
    }
  };

  const validateForm = () => {
    const newErrors = {
      deckName: false,
      archetype: false,
      decklist: false
    };

    // Validate decklist URL if provided
    if (formData.decklist.trim() && !validateManaboxUrl(formData.decklist.trim())) {
      newErrors.decklist = true;
    }

    setErrors(newErrors);
    return !Object.values(newErrors).some(error => error);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      const formattedDecklistUrl = formData.decklist.trim() ? formatManaboxUrl(formData.decklist.trim()) : null;

      await updateRegistration({
        registrationId,
        deckName: formData.deckName.trim() || null,
        archetypeId: formData.archetype.trim() || null,
        deckListUrl: formattedDecklistUrl
      });

      setIsSubmitted(true);
    } catch (error) {
      console.error('Error updating registration:', error);
    }
  };

  const handleDelete = async () => {
    try {
      await deleteRegistration(registrationId);
      onClose();
    } catch (error) {
      console.error('Error deleting registration:', error);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 rounded-xl border border-blue-500/30 w-full max-w-2xl max-h-[85vh] overflow-hidden">
        {/* Header del Modale */}
        <div className="p-4 border-b border-blue-500/20 flex justify-between items-center">
          <h3 className="text-xl font-bold flex items-center gap-2">
            <User size={20} />
            {isSubmitted ? "Registrazione Aggiornata" : `Gestisci Registrazione - ${registration?.players?.name || 'Caricamento...'}`}
          </h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-blue-900/50 rounded-full transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Contenuto del Modale */}
        <div className="p-4 overflow-y-auto max-h-[calc(85vh-4rem)]">
          {isSubmitted ? (
            /* Messaggio di Successo */
            <div className="text-center py-8 space-y-4">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto">
                <Check size={32} className="text-green-400" />
              </div>
              <div>
                <h4 className="text-xl font-bold text-green-400 mb-2">Registrazione Aggiornata!</h4>
                <p className="text-blue-200 mb-4">
                  I dati della registrazione sono stati aggiornati con successo.
                </p>
                <button
                  onClick={onClose}
                  className="px-6 py-2 bg-blue-500 hover:bg-blue-600 rounded-lg font-medium transition-colors"
                >
                  Chiudi
                </button>
              </div>
            </div>
          ) : isLoadingRegistration ? (
            /* Loading State */
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-400 mx-auto mb-4" />
              <p className="text-blue-200">Caricamento dati registrazione...</p>
            </div>
          ) : showDeleteConfirm ? (
            /* Conferma Eliminazione */
            <div className="text-center py-8 space-y-4">
              <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto">
                <AlertCircle size={32} className="text-red-400" />
              </div>
              <div>
                <h4 className="text-xl font-bold text-red-400 mb-2">Conferma Eliminazione</h4>
                <p className="text-blue-200 mb-6">
                  Sei sicuro di voler eliminare la registrazione di <strong>{registration?.players?.name}</strong>?
                  <br />
                  <span className="text-red-300">Questa azione non può essere annullata.</span>
                </p>
                <div className="flex gap-4 justify-center">
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    className="px-6 py-2 bg-gray-500 hover:bg-gray-600 rounded-lg font-medium transition-colors"
                  >
                    Annulla
                  </button>
                  <button
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="px-6 py-2 bg-red-500 hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg font-medium transition-colors flex items-center gap-2"
                  >
                    {isDeleting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white" />
                        Eliminazione...
                      </>
                    ) : (
                      <>
                        <Trash2 size={16} />
                        Elimina
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          ) : (
            /* Form di Modifica */
            <>
              {/* Info Giocatore */}
              <div className="mb-6 p-4 bg-blue-900/30 rounded-lg border border-blue-500/20">
                <h4 className="font-semibold text-blue-200 mb-2">Informazioni Giocatore</h4>
                <p className="text-white"><strong>Nome:</strong> {registration?.players?.name}</p>
                <p className="text-blue-200 text-sm">
                  <strong>Data Registrazione:</strong> {registration?.registration_date ? new Date(registration.registration_date).toLocaleDateString('it-IT') : 'N/A'}
                </p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Nome Deck */}
                <div>
                  <label className="block text-sm font-medium text-blue-200 mb-2">
                    Nome Deck
                  </label>
                  <input
                    type="text"
                    value={formData.deckName}
                    onChange={(e) => handleInputChange('deckName', e.target.value)}
                    className="w-full px-3 py-2 bg-blue-900/50 border border-blue-500/30 rounded-lg text-white placeholder-blue-300 focus:outline-none focus:border-blue-400"
                    placeholder="es. Aggro Rosso, Control Blu..."
                  />
                </div>

                {/* Archetipo */}
                <div>
                  <label className="block text-sm font-medium text-blue-200 mb-2">
                    Archetipo
                  </label>
                  <Select
                    value={formData.archetype}
                    onChange={(value) => handleInputChange('archetype', value)}
                    options={[
                      { value: "", label: "Seleziona un archetipo (opzionale)" },
                      ...(archetypes?.map(arch => ({
                        value: arch.id,
                        label: arch.name
                      })) || [])
                    ]}
                    disabled={isLoadingArchetypes}
                    className="w-full"
                  />
                </div>

                {/* Decklist URL */}
                <div>
                  <label className="block text-sm font-medium text-blue-200 mb-2">
                    Link Decklist (Manabox)
                  </label>
                  <input
                    type="url"
                    value={formData.decklist}
                    onChange={(e) => handleInputChange('decklist', e.target.value)}
                    className={`w-full px-3 py-2 bg-blue-900/50 border rounded-lg text-white placeholder-blue-300 focus:outline-none focus:border-blue-400 ${
                      errors.decklist ? 'border-red-500' : 'border-blue-500/30'
                    }`}
                    placeholder="https://manabox.app/decks/..."
                  />
                  {errors.decklist && (
                    <p className="mt-1 text-xs text-red-400">
                      Inserisci un link valido di Manabox
                    </p>
                  )}
                  <p className="mt-1 text-xs text-blue-400">
                    Inserisci il link alla decklist su Manabox
                  </p>
                </div>

                {/* Bottoni Azione */}
                <div className="flex gap-3 pt-4">
                  <button
                    type="submit"
                    disabled={isUpdating}
                    className="flex-1 py-3 bg-blue-500 hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
                  >
                    {isUpdating ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white" />
                        Aggiornamento...
                      </>
                    ) : (
                      <>
                        <Save size={16} />
                        Salva Modifiche
                      </>
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowDeleteConfirm(true)}
                    className="px-6 py-3 bg-red-500 hover:bg-red-600 rounded-lg font-medium transition-colors flex items-center gap-2"
                  >
                    <Trash2 size={16} />
                    Elimina
                  </button>
                </div>
              </form>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
