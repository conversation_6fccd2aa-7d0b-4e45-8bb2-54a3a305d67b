"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, RotateCcw, CheckCircle } from "lucide-react";
import { Select } from "@/components/ui/Select";
import {
  PairingWithPlayers,
  getPlayerDisplayName
} from "@/lib/mock/tournamentOrganizerMock";




interface ResultsInterfaceProps {
  pairings: PairingWithPlayers[];
  onCompleteRound?: () => void;
  className?: string;
  isHistorical?: boolean;
}

const RESULT_OPTIONS = [
  { value: '', label: 'Seleziona risultato' },
  { value: '2-0', label: '2-0' },
  { value: '2-1', label: '2-1' },
  { value: '1-2', label: '1-2' },
  { value: '0-2', label: '0-2' },
  { value: '1-0', label: '1-0' },
  { value: '0-1', label: '0-1' },
  { value: '0-0', label: '0-0 (Pareggio)' }
];

export function ResultsInterface({
  pairings,
  onCompleteRound,
  className = "",
  isHistorical = false
}: ResultsInterfaceProps) {
  const [results, setResults] = useState<{ [pairingId: string]: string }>({});

  // Initialize results from pairings
  useEffect(() => {
    const initialResults: { [pairingId: string]: string } = {};
    pairings.forEach(pairing => {
      if (pairing.result) {
        initialResults[pairing.id] = pairing.result;
      } else if (pairing.is_bye) {
        initialResults[pairing.id] = 'bye';
      }
    });
    setResults(initialResults);
  }, [pairings]);

  const handleResultChange = (pairingId: string, result: string) => {
    setResults(prev => ({
      ...prev,
      [pairingId]: result
    }));
  };

  const getCompletedMatches = () => {
    return pairings.filter(p => results[p.id] && results[p.id] !== '').length;
  };

  const getTotalMatches = () => {
    return pairings.length;
  };

  const canCompleteRound = () => {
    return getCompletedMatches() === getTotalMatches();
  };





  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-white">Inserimento Risultati</h3>
          <div className="text-sm text-blue-300">
            {getCompletedMatches()} di {getTotalMatches()} match completati
          </div>
        </div>


      </div>

      <div className="flex-1 flex gap-4 overflow-hidden">
        {/* Results Entry */}
        <div className="flex-1 flex flex-col">
          {/* Results List */}
          <div className="flex-1 overflow-y-auto space-y-3">
            {pairings.map((pairing) => (
              <div
                key={pairing.id}
                className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-4">
                      <div className="text-sm font-medium text-blue-200">
                        {pairing.is_bye ? 'BYE' : `Tavolo ${pairing.table_number}`}
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <span className="text-white text-sm">{getPlayerDisplayName(pairing.player1)}</span>
                        {!pairing.is_bye && pairing.player2 && (
                          <>
                            <span className="text-blue-400 text-xs">VS</span>
                            <span className="text-white text-sm">{getPlayerDisplayName(pairing.player2)}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="w-48">
                    {pairing.is_bye ? (
                      <div className="text-center text-yellow-400 text-sm font-medium">
                        BYE (3 punti)
                      </div>
                    ) : (
                      <Select
                        value={results[pairing.id] || ''}
                        onChange={isHistorical ? () => {} : (value) => handleResultChange(pairing.id, value)}
                        options={RESULT_OPTIONS}
                        placeholder={isHistorical ? "Risultato storico" : "Seleziona risultato"}
                        hasError={false}
                        disabled={isHistorical}
                      />
                    )}
                  </div>

                  {results[pairing.id] && results[pairing.id] !== '' && (
                    <CheckCircle size={16} className="text-green-400 ml-2" />
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Complete Round Button or Historical Message */}
          <div className="mt-4">
            {!isHistorical && onCompleteRound ? (
              <button
                onClick={onCompleteRound}
                disabled={!canCompleteRound()}
                className={`
                  w-full py-3 rounded-lg font-medium transition-all duration-200
                  flex items-center justify-center gap-2
                  ${canCompleteRound()
                    ? 'bg-green-600 hover:bg-green-700 text-white hover:scale-105 active:scale-95'
                    : 'bg-gray-600 text-gray-300 cursor-not-allowed'
                  }
                `}
              >
                <Trophy size={20} />
                Completa Round
              </button>
            ) : (
              <div className="w-full py-3 rounded-lg bg-blue-900/20 border border-blue-500/30 text-center">
                <div className="flex items-center justify-center gap-2 text-blue-300">
                  <CheckCircle size={20} />
                  <span className="font-medium">Round Completato</span>
                </div>
                <div className="text-xs text-blue-400 mt-1">
                  Visualizzazione storica dei risultati
                </div>
              </div>
            )}
          </div>
        </div>


      </div>
    </div>
  );
}
