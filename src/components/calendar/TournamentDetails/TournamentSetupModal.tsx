"use client";

import React, { useState, useEffect } from "react";
import { Modal } from "@/components/ui/Modal";
import { FormField, Input } from "@/components/ui/FormField";

interface TournamentSetupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (settings: TournamentSettings) => void;
  initialSettings?: TournamentSettings;
}

export interface TournamentSettings {
  rounds: number;
  roundDurationMinutes: number;
}

export function TournamentSetupModal({
  isOpen,
  onClose,
  onSave,
  initialSettings
}: TournamentSetupModalProps) {
  const [formData, setFormData] = useState<TournamentSettings>({
    rounds: initialSettings?.rounds || 4,
    roundDurationMinutes: initialSettings?.roundDurationMinutes || 50
  });

  const [errors, setErrors] = useState<{
    rounds?: string;
    roundDurationMinutes?: string;
  }>({});

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setFormData({
        rounds: initialSettings?.rounds || 4,
        roundDurationMinutes: initialSettings?.roundDurationMinutes || 50
      });
      setErrors({});
    }
  }, [isOpen, initialSettings]);

  const validateForm = (): boolean => {
    const newErrors: typeof errors = {};

    // Validate rounds
    if (formData.rounds < 1 || formData.rounds > 10) {
      newErrors.rounds = "Il numero di round deve essere tra 1 e 10";
    }

    // Validate round duration
    if (formData.roundDurationMinutes < 30 || formData.roundDurationMinutes > 120) {
      newErrors.roundDurationMinutes = "La durata del round deve essere tra 30 e 120 minuti";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSave(formData);
      onClose();
    }
  };

  const handleInputChange = (field: keyof TournamentSettings, value: string) => {
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue)) {
      setFormData(prev => ({
        ...prev,
        [field]: numValue
      }));
      
      // Clear error for this field when user starts typing
      if (errors[field]) {
        setErrors(prev => ({
          ...prev,
          [field]: undefined
        }));
      }
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Configurazione Tournament Organizer"
      size="md"
    >
      <form onSubmit={handleSubmit}>
        <Modal.Body>
          <div className="space-y-4">
            <p className="text-blue-200 text-sm mb-4">
              Configura le impostazioni per il tournament organizer. Potrai modificare queste impostazioni in seguito.
            </p>

            <FormField
              label="Numero di Round"
              error={errors.rounds}
              required
              hint="Inserisci un numero tra 1 e 10"
            >
              <Input
                type="number"
                min="1"
                max="10"
                value={formData.rounds.toString()}
                onChange={(e) => handleInputChange('rounds', e.target.value)}
                error={!!errors.rounds}
                placeholder="4"
              />
            </FormField>

            <FormField
              label="Durata Round (minuti)"
              error={errors.roundDurationMinutes}
              required
              hint="Inserisci un numero tra 30 e 120 minuti"
            >
              <Input
                type="number"
                min="30"
                max="120"
                step="5"
                value={formData.roundDurationMinutes.toString()}
                onChange={(e) => handleInputChange('roundDurationMinutes', e.target.value)}
                error={!!errors.roundDurationMinutes}
                placeholder="50"
              />
            </FormField>

            <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
              <h4 className="text-sm font-medium text-blue-200 mb-2">Anteprima Configurazione</h4>
              <div className="text-xs text-blue-300 space-y-1">
                <p>• {formData.rounds} round{formData.rounds !== 1 ? 's' : ''} di {formData.roundDurationMinutes} minuti ciascuno</p>
                <p>• Durata totale stimata: {formData.rounds * formData.roundDurationMinutes} minuti ({Math.round((formData.rounds * formData.roundDurationMinutes) / 60 * 10) / 10} ore)</p>
                <p>• Sistema di accoppiamento: Swiss pairing</p>
              </div>
            </div>
          </div>
        </Modal.Body>
        
        <Modal.Footer>
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
          >
            Annulla
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
          >
            Salva Configurazione
          </button>
        </Modal.Footer>
      </form>
    </Modal>
  );
}
