"use client";

import React from "react";
import { Modal } from "@/components/ui/Modal";

interface TournamentCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  tournamentTitle: string;
}

export function TournamentCreationModal({
  isOpen,
  onClose,
  onConfirm,
  tournamentTitle
}: TournamentCreationModalProps) {
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Crea Tournament Organizer"
      size="md"
    >
      <Modal.Body>
        <div className="text-center space-y-4">
          <p className="text-white text-base">
            Vuoi creare un tournament organizer per questo evento?
          </p>
          <p className="text-blue-300 text-sm">
            <strong>{tournamentTitle}</strong>
          </p>
          <p className="text-blue-200 text-sm">
            Questo ti permetterà di gestire accoppiamenti, round e risultati del torneo.
          </p>
        </div>
      </Modal.Body>
      
      <Modal.Footer>
        <button
          onClick={onClose}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
        >
          Annulla
        </button>
        <button
          onClick={handleConfirm}
          className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
        >
          Procedi
        </button>
      </Modal.Footer>
    </Modal>
  );
}
