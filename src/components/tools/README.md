# Tools - Life Counter e Mana Counter

## Persistenza degli Stati

I tools Life Counter e Mana Counter ora mantengono i loro stati anche quando l'utente:
- Chiude e riapre la pagina
- Refresha la pagina
- Naviga verso altre pagine e torna indietro

### Come Funziona

Gli stati vengono salvati automaticamente nel `localStorage` del browser utilizzando due hook personalizzati:

#### `usePersistedLifeCounter`
- **Chiave localStorage**: `lpa-life-counter-state`
- **Dati salvati**: 
  - `player1Life`: Punti vita del giocatore 1
  - `player2Life`: Punti vita del giocatore 2
- **Valori di default**: Entrambi i giocatori iniziano con 20 punti vita

#### `usePersistedManaCounter`
- **Chiavi localStorage**: 
  - `lpa-mana-counter-state`: Contatori di mana
  - `lpa-mana-counter-visible-bars`: Barre visibili
- **Dati salvati**:
  - Contatori per ogni tipo di mana (white, blue, black, red, green, colorless)
  - Configurazione delle barre visibili nel fullscreen mode
- **Valori di default**: 
  - Tutti i contatori di mana a 0
  - Barre visibili: white, blue, black, red, green (colorless nascosto di default)

### Reset degli Stati

Gli stati possono essere resettati ai valori di default utilizzando i pulsanti "Reset" presenti in entrambi i tools:
- **Life Counter**: Pulsante "Reset Life (20)" nel menu settings
- **Mana Counter**: Pulsante "Resetta Mana" 

### Componenti Aggiornati

1. **LifeCounter.tsx** - Versione base del life counter
2. **FullscreenLifeCounter.tsx** - Versione fullscreen del life counter
3. **ManaCounter.tsx** - Versione base del mana counter
4. **FullscreenManaCounter.tsx** - Versione fullscreen del mana counter

### Hook Personalizzati

1. **usePersistedLifeCounter.ts** - Gestisce la persistenza del life counter
2. **usePersistedManaCounter.ts** - Gestisce la persistenza del mana counter

### Loading States

Entrambi i tools mostrano un indicatore di caricamento ("Caricamento...") mentre i dati vengono recuperati dal localStorage, garantendo una UX fluida.

### Compatibilità

La persistenza funziona su tutti i browser moderni che supportano localStorage. In caso di errori (es. localStorage disabilitato), i tools utilizzano automaticamente i valori di default senza crashare.

### Testing

È disponibile un file di test (`__tests__/persistence.test.tsx`) per verificare manualmente il funzionamento della persistenza durante lo sviluppo.
