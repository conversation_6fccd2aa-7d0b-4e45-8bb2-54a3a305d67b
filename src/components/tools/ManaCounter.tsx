"use client";

import { usePersisted<PERSON>anaCounter, ManaType } from "@/lib/hooks/usePersistedManaCounter";

const ManaCounter = () => {
  const { manaCount, handleManaChange, resetAllMana, isLoaded } = usePersistedManaCounter();

  // Mostra un loading state fino a quando i dati non sono caricati
  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-white text-lg">Caricamento...</div>
      </div>
    );
  }

  // Configurazione dei simboli di mana e colori
  const manaSymbols: Record<ManaType, { symbol: string; bgColor: string; textColor: string; borderColor: string; name: string }> = {
    white: { 
      symbol: "W", 
      bgColor: "bg-yellow-100", 
      textColor: "text-black",
      borderColor: "border-yellow-300",
      name: "<PERSON><PERSON><PERSON>"
    },
    blue: { 
      symbol: "U", 
      bgColor: "bg-blue-600", 
      textColor: "text-white",
      borderColor: "border-blue-400",
      name: "Blu"
    },
    black: { 
      symbol: "B", 
      bgColor: "bg-black", 
      textColor: "text-gray-300",
      borderColor: "border-gray-600",
      name: "Nero"
    },
    red: { 
      symbol: "R", 
      bgColor: "bg-red-600", 
      textColor: "text-white",
      borderColor: "border-red-400",
      name: "Rosso"
    },
    green: { 
      symbol: "G", 
      bgColor: "bg-green-600", 
      textColor: "text-white",
      borderColor: "border-green-400",
      name: "Verde"
    },
    colorless: { 
      symbol: "C", 
      bgColor: "bg-gray-400", 
      textColor: "text-gray-800",
      borderColor: "border-gray-300",
      name: "Incolore"
    },
  };

  return (
    <div className="flex flex-col h-full py-4">
      <div className="grid grid-cols-2 md:grid-cols-3 gap-6 mb-6">
        {(Object.keys(manaCount) as ManaType[]).map((type) => (
          <div 
            key={type}
            className="flex flex-col items-center justify-center bg-black/30 rounded-lg p-5 border border-blue-500/30 hover:border-blue-400/50 transition-all shadow-lg backdrop-blur-sm"
          >
            <div className="mb-3">
              <div 
                className={`w-14 h-14 rounded-full flex items-center justify-center font-bold text-2xl ${manaSymbols[type].bgColor} ${manaSymbols[type].textColor} border-2 ${manaSymbols[type].borderColor} shadow-lg`}
              >
                {manaSymbols[type].symbol}
              </div>
            </div>
            
            <div className="text-sm font-medium text-blue-200 mb-2">
              {manaSymbols[type].name}
            </div>

            <div className="text-4xl font-bold text-white mb-4">
              {manaCount[type]}
            </div>

            <div className="flex space-x-4">
              <button
                onClick={() => handleManaChange(type, -1)}
                className="w-10 h-10 bg-blue-900/70 hover:bg-blue-800 text-white rounded-full flex items-center justify-center shadow-md transform transition-transform hover:scale-105 active:scale-95"
                disabled={manaCount[type] === 0}
              >
                -
              </button>
              <button
                onClick={() => handleManaChange(type, 1)}
                className="w-10 h-10 bg-blue-900/70 hover:bg-blue-800 text-white rounded-full flex items-center justify-center shadow-md transform transition-transform hover:scale-105 active:scale-95"
              >
                +
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="py-4 flex justify-center border-t border-blue-500/30">
        <button
          onClick={resetAllMana}
          className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md shadow-md transition-colors transform hover:scale-105 active:scale-95"
        >
          Resetta Mana
        </button>
      </div>
    </div>
  );
};

export default ManaCounter; 