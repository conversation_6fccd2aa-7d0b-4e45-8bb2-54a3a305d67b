"use client";

import { usePersistedLifeCounter } from "@/lib/hooks/usePersistedLifeCounter";

const LifeCounter = () => {
  const { player1Life, player2Life, handleLifeChange, resetLife, isLoaded } = usePersistedLifeCounter();

  // Mostra un loading state fino a quando i dati non sono caricati
  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-white text-lg">Caricamento...</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full relative">
      {/* Player 2 (Invertito) */}
      <div 
        className={`flex-1 flex flex-col justify-center items-center transform rotate-180 rounded-t-lg transition-colors duration-300 ${
          player2Life <= 0 
            ? 'bg-red-900/40 border-red-500/50' 
            : 'bg-gradient-to-t from-blue-900/30 to-sky-900/30'
        }`}
      >
        <div className="w-full max-w-md px-6 py-4">
          <div className="flex justify-between items-center mb-6">
            <button
              onClick={() => handleLifeChange(2, -1)}
              className="w-16 h-16 bg-blue-900/70 hover:bg-blue-800 text-white text-3xl rounded-full shadow-lg transform transition-transform hover:scale-105 active:scale-95"
            >
              -
            </button>
            <div className="flex-1 text-center">
              <span className={`text-7xl font-bold ${player2Life <= 0 ? 'text-red-300' : 'text-white'}`}>
                {player2Life}
              </span>
            </div>
            <button
              onClick={() => handleLifeChange(2, 1)}
              className="w-16 h-16 bg-blue-900/70 hover:bg-blue-800 text-white text-3xl rounded-full shadow-lg transform transition-transform hover:scale-105 active:scale-95"
            >
              +
            </button>
          </div>
          <div className="flex justify-center space-x-4">
            <button
              onClick={() => handleLifeChange(2, -5)}
              className="px-4 py-2 bg-blue-900/70 hover:bg-blue-800 text-white rounded-md shadow-md transform transition-transform hover:scale-105 active:scale-95"
            >
              -5
            </button>
            <button
              onClick={() => handleLifeChange(2, 5)}
              className="px-4 py-2 bg-blue-900/70 hover:bg-blue-800 text-white rounded-md shadow-md transform transition-transform hover:scale-105 active:scale-95"
            >
              +5
            </button>
          </div>
        </div>
      </div>
      
      {/* Separatore e reset */}
      <div className="py-4 flex justify-center bg-black/30 border-y border-blue-500/30 backdrop-blur-sm z-10">
        <button
          onClick={resetLife}
          className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md shadow-md transition-colors transform hover:scale-105 active:scale-95"
        >
          Resetta (20)
        </button>
      </div>
      
      {/* Player 1 (Normale) */}
      <div 
        className={`flex-1 flex flex-col justify-center items-center rounded-b-lg transition-colors duration-300 ${
          player1Life <= 0 
            ? 'bg-red-900/40 border-red-500/50' 
            : 'bg-gradient-to-b from-blue-900/30 to-sky-900/30'
        }`}
      >
        <div className="w-full max-w-md px-6 py-4">
          <div className="flex justify-between items-center mb-6">
            <button
              onClick={() => handleLifeChange(1, -1)}
              className="w-16 h-16 bg-blue-900/70 hover:bg-blue-800 text-white text-3xl rounded-full shadow-lg transform transition-transform hover:scale-105 active:scale-95"
            >
              -
            </button>
            <div className="flex-1 text-center">
              <span className={`text-7xl font-bold ${player1Life <= 0 ? 'text-red-300' : 'text-white'}`}>
                {player1Life}
              </span>
            </div>
            <button
              onClick={() => handleLifeChange(1, 1)}
              className="w-16 h-16 bg-blue-900/70 hover:bg-blue-800 text-white text-3xl rounded-full shadow-lg transform transition-transform hover:scale-105 active:scale-95"
            >
              +
            </button>
          </div>
          <div className="flex justify-center space-x-4">
            <button
              onClick={() => handleLifeChange(1, -5)}
              className="px-4 py-2 bg-blue-900/70 hover:bg-blue-800 text-white rounded-md shadow-md transform transition-transform hover:scale-105 active:scale-95"
            >
              -5
            </button>
            <button
              onClick={() => handleLifeChange(1, 5)}
              className="px-4 py-2 bg-blue-900/70 hover:bg-blue-800 text-white rounded-md shadow-md transform transition-transform hover:scale-105 active:scale-95"
            >
              +5
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LifeCounter; 