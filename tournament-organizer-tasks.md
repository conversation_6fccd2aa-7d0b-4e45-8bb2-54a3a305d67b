# Tournament Organizer Feature - Task Document

## Overview
This document provides a structured task breakdown for implementing the Tournament Organizer feature. All tasks are organized hierarchically and should be completed in the order specified to ensure proper dependencies are met.

## Task Structure

### 🎯 Main Goal: Tournament Organizer Feature Implementation
**Status**: Not Started  
**Description**: Implement the complete Tournament Organizer feature for admin users as outlined in the planning document

---

## Phase 1: UI Components Development
**Priority**: High (Start here - no database needed)
**Estimated Time**: 5-7 days

### 1.1 Tournament creation button
- **File**: `src/components/calendar/TournamentDetails/TournamentDetails.tsx`
- **Requirements**:
  - Green square button with "+" icon
  - Admin-only visibility using `useIsAdmin()`
  - Tooltip "create tournament"
  - State change to "eye" icon after creation
- **Dependencies**: None (uses existing admin system)
- **Validation**: But<PERSON> appears only for admins, correct styling

### 1.2 Confirmation modal
- **File**: `src/components/calendar/TournamentDetails/TournamentCreationModal.tsx`
- **Requirements**:
  - Use existing `Modal` component
  - "Do you want to create a tournament for this event?" text
  - Cancel (blue) and Proceed (green) buttons
  - Consistent styling with existing modals
- **Dependencies**: 1.1 completed
- **Validation**: Modal opens/closes correctly, proper button actions

### 1.3 Tournament setup modal
- **File**: `src/components/calendar/TournamentDetails/TournamentSetupModal.tsx`
- **Requirements**:
  - Number of rounds input (1-10 range)
  - Round duration input (30-120 minutes)
  - Form validation using existing `FormField` and `Input`
  - Save settings to local state (no database yet)
- **Dependencies**: 1.2 completed
- **Validation**: Form validation works, settings saved to local state

### 1.4 Tournament organizer panel
- **File**: `src/components/calendar/TournamentDetails/TournamentOrganizerPanel.tsx`
- **Requirements**:
  - Fullscreen panel (`size="full"`)
  - Close "X" button
  - Settings gear icon
  - Layout: player list (left), pairings (center), controls (right)
- **Dependencies**: 1.3 completed
- **Validation**: Panel opens fullscreen, proper layout on desktop/mobile

### 1.5 Timer component
- **File**: `src/components/calendar/TournamentDetails/TournamentTimer.tsx`
- **Requirements**:
  - Large MM:SS display at top center
  - Color changes (green → yellow → red)
  - Start/pause/reset controls
  - Integration with round duration settings
- **Dependencies**: 1.4 completed
- **Validation**: Timer functions correctly, visual indicators work

### 1.6 Pairing interface
- **File**: `src/components/calendar/TournamentDetails/PairingInterface.tsx`
- **Requirements**:
  - Match cards showing player pairings
  - Table numbers
  - Bye indicators
  - Generate/Confirm/Cancel pairing buttons
- **Dependencies**: 1.4 completed
- **Validation**: Pairings display correctly, buttons function

### 1.7 Results entry interface
- **File**: `src/components/calendar/TournamentDetails/ResultsInterface.tsx`
- **Requirements**:
  - Result dropdowns: 2-0, 2-1, 1-2, 0-2, 1-0, 0-1, 0-0
  - Bulk result entry
  - Result validation
  - Final standings table
- **Dependencies**: 1.6 completed
- **Validation**: Results can be entered and saved to local state

---

## Phase 2: Business Logic Implementation (Mock Data)
**Priority**: High
**Estimated Time**: 4-6 days

### 2.1 Mock data creation
- **File**: `src/lib/mock/tournamentOrganizerMock.ts`
- **Requirements**:
  - Mock player data (names, IDs)
  - Mock tournament settings
  - Mock round and pairing data
  - Mock results and standings
- **Dependencies**: Phase 1 UI components
- **Validation**: Comprehensive mock data for testing

### 2.2 Tournament services (Mock)
- **File**: `src/lib/services/tournamentOrganizerMock.ts`
- **Requirements**:
  - Mock CRUD operations using local state
  - Same interface as future database service
  - Error handling and validation
  - TypeScript types
- **Dependencies**: 2.1 completed
- **Validation**: All operations work with mock data

### 2.3 Swiss pairing algorithm
- **File**: `src/lib/utils/swissPairing.ts`
- **Requirements**:
  - Random first round pairing
  - Point-based subsequent rounds
  - Bye assignment logic
  - Avoid repeat pairings
  - Handle odd number of players
- **Dependencies**: 2.2 completed
- **Validation**: Algorithm produces valid pairings for various scenarios

### 2.4 Round management system
- **File**: `src/lib/utils/roundManagement.ts`
- **Requirements**:
  - Round progression logic
  - State transitions (pending → active → completed)
  - Timer integration
  - Round validation using local state
- **Dependencies**: 2.3 completed
- **Validation**: Rounds progress correctly, state management works

### 2.5 Scoring and statistics
- **File**: `src/lib/utils/tournamentScoring.ts`
- **Requirements**:
  - Match point calculation (3-1-0 system)
  - Game win/loss tracking
  - OMW% and GWP% calculation
  - Final standings generation
- **Dependencies**: 2.4 completed
- **Validation**: Scoring calculations are accurate

---

## Phase 3: Frontend Testing & Integration
**Priority**: Medium
**Estimated Time**: 3-4 days

### 3.1 Mock data integration
- **Files**: Connect all UI components with mock services
- **Requirements**:
  - Connect UI components with mock data
  - Test data flow through all components
  - Validate state management
  - Error handling with mock data
- **Dependencies**: Phase 1, 2 completed
- **Validation**: All UI works with mock data

### 3.2 Admin permission testing
- **Requirements**:
  - Test all admin-only UI features
  - Test unauthorized access attempts (UI level)
  - Validate admin-only button visibility
  - Test permission-based component rendering
- **Dependencies**: 3.1 completed
- **Validation**: Admin-only features properly restricted

### 3.3 End-to-end UI flow testing
- **Requirements**:
  - Test complete tournament UI flow from creation to results
  - Multiple scenarios (different player counts, rounds)
  - Error scenarios and edge cases
  - UI state consistency validation
- **Dependencies**: 3.2 completed
- **Validation**: Full UI tournament flow works correctly

### 3.4 Mobile responsiveness testing
- **Requirements**:
  - Test all components on mobile devices
  - Verify touch interactions
  - Check responsive layouts
  - Performance on mobile
- **Dependencies**: 3.3 completed
- **Validation**: Mobile experience is usable

### 3.5 Performance optimization
- **Requirements**:
  - Optimize UI performance for large tournaments
  - Implement local caching strategies
  - Component optimization
  - Bundle size considerations
- **Dependencies**: 3.4 completed
- **Validation**: UI performance meets requirements

---

## Phase 4: Database Schema Setup (FINAL PHASE)
**Priority**: Low (Only after UI is complete and tested)
**Estimated Time**: 2-3 days

### 4.1 Create new database tables migration
- **File**: `supabase/migrations/YYYYMMDD_tournament_organizer_tables.sql`
- **Tables to create**:
  - `tournament_organizer_settings`
  - `tournament_rounds`
  - `tournament_pairings`
  - `tournament_player_stats`
- **Dependencies**: All UI and mock logic completed and tested
- **Validation**: Tables created with proper constraints and relationships

### 4.2 Add columns to tournaments table
- **File**: Same migration or separate file
- **Changes**:
  - `has_organizer BOOLEAN DEFAULT FALSE`
  - `organizer_created_at TIMESTAMP WITH TIME ZONE`
- **Dependencies**: 4.1 completed
- **Validation**: Columns added without breaking existing functionality

### 4.3 Implement RLS policies
- **File**: Same migration or separate file
- **Policies needed**:
  - Admin-only access during tournament
  - Public read access after completion
  - Leverage existing `is_admin()` function
- **Dependencies**: 4.1, 4.2 completed
- **Validation**: Admin can access, non-admin cannot during active tournament

### 4.4 Test database schema
- **Actions**:
  - Apply migration to development environment
  - Test admin access patterns
  - Verify constraint enforcement
  - Test RLS policies
- **Dependencies**: 4.1, 4.2, 4.3 completed
- **Validation**: All database operations work as expected

### 4.5 Replace mock services with database
- **File**: Update `src/lib/services/tournamentOrganizer.ts`
- **Requirements**:
  - Replace mock operations with real Supabase calls
  - Maintain same interface as mock service
  - Add real-time subscriptions
  - Error handling for database operations
- **Dependencies**: 4.4 completed
- **Validation**: All functionality works with real database

---

## Success Criteria

### Functional Requirements
- ✅ Admin can create tournament organizer for any event
- ✅ Tournament setup with configurable rounds and duration
- ✅ Swiss pairing system works correctly
- ✅ Round management with timer functionality
- ✅ Result entry and final standings generation
- ✅ Real-time updates for multiple admins

### Technical Requirements
- ✅ All new database tables and RLS policies implemented
- ✅ Integration with existing codebase maintained
- ✅ Mobile responsive design
- ✅ Performance optimized for tournaments up to 50+ players
- ✅ Security requirements met (admin-only during tournament)

### Quality Requirements
- ✅ Code follows existing patterns and conventions
- ✅ Comprehensive error handling
- ✅ TypeScript types for all new code
- ✅ Consistent UI/UX with existing components
- ✅ Thorough testing coverage

---

## Notes
- Each task should be completed and tested before moving to the next
- Regular testing against the planning document requirements
- Consider creating feature flags for gradual rollout
- Document any deviations from the original plan
