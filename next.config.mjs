/** @type {import('next').NextConfig} */
const nextConfig = {
  async headers() {
    // Enhanced CSP with restricted external connections for better security
    // Only allows connections to trusted domains (Supabase and essential CDNs)
    const csp = [
      "default-src 'self'",
      "base-uri 'self'",
      "frame-ancestors 'none'",
      "form-action 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' blob:",
      // Restrict connect-src to only trusted domains
      "connect-src 'self' https://*.supabase.co wss://*.supabase.co https://api.supabase.io",
      // Restrict img-src to trusted image sources
      "img-src 'self' data: https://*.supabase.co https://images.unsplash.com https://cdn.jsdelivr.net",
      "style-src 'self' 'unsafe-inline'",
      // Restrict font-src to trusted font sources
      "font-src 'self' data: https://fonts.googleapis.com https://fonts.gstatic.com",
      "worker-src 'self' blob:",
      "object-src 'none'",
    ].join('; ');

    return [
      {
        source: '/(.*)',
        headers: [
          { key: 'Content-Security-Policy', value: csp },
          { key: 'Strict-Transport-Security', value: 'max-age=31536000; includeSubDomains; preload' },
          { key: 'X-Frame-Options', value: 'DENY' },
          { key: 'X-Content-Type-Options', value: 'nosniff' },
          { key: 'Referrer-Policy', value: 'no-referrer' },
          { key: 'Permissions-Policy', value: 'geolocation=(), microphone=(), camera=(), payment=()' },
          { key: 'Cross-Origin-Opener-Policy', value: 'same-origin' },
          { key: 'Cross-Origin-Resource-Policy', value: 'same-origin' },
          { key: 'Cross-Origin-Embedder-Policy', value: 'require-corp' },
        ],
      },
    ];
  },
};

export default nextConfig;

