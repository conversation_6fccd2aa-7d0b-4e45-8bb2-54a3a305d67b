import type { NextConfig } from "next";

// Get allowed development origins from environment variable or use defaults
const getAllowedDevOrigins = (): string[] => {
  const envOrigins = process.env.ALLOWED_DEV_ORIGINS;
  if (envOrigins) {
    return envOrigins.split(',').map(origin => origin.trim());
  }

  // Default development origins
  return [
    'localhost:3000',
    'http://localhost:3000',
    '127.0.0.1:3000',
    'http://127.0.0.1:3000'
  ];
};

const isDevelopment = process.env.NODE_ENV === 'development';

const nextConfig: NextConfig = {
  // Allow development from LAN IP or custom origins
  // Only include this in development or when explicitly configured
  allowedDevOrigins: getAllowedDevOrigins(),

  typescript: {
    // Only ignore build errors in development - enforce in production
    ignoreBuildErrors: isDevelopment,
  },
  eslint: {
    // Only ignore linting during builds in development - enforce in production
    ignoreDuringBuilds: isDevelopment,
  },
};

export default nextConfig;
